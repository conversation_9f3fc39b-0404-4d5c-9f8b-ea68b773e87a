"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import emailjs from '@emailjs/browser'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Loader2, Send, Mail, Phone, Code, Users, MessageCircle } from "lucide-react"

// Static profile data
const profile = {
  name: "Yessine JAOUA",
  email: "<EMAIL>",
  phone: "+212 620 72 26 02",
  location: "Your Location",
}

// Static social links
const socialLinks = [
  { id: 1, name: "GitHub", url: "https://github.com/apachi1444", icon: <Code className="h-6 w-6" /> },
  { id: 2, name: "LinkedIn", url: "https://linkedin.com/in/yessine-jaoua", icon: <Users className="h-6 w-6" /> },
  { id: 3, name: "Twitter", url: "https://twitter.com/JawaYessine", icon: <MessageCircle className="h-6 w-6" /> },
]

const formSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  message: z.string().min(10, "Message must be at least 10 characters"),
})

type FormData = z.infer<typeof formSchema>

export default function ContactSection() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [submitError, setSubmitError] = useState(false)

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
  })

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    setSubmitSuccess(false)
    setSubmitError(false)

    try {
      // EmailJS configuration from environment variables
      const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID
      const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID
      const publicKey = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY

      // Check if all required environment variables are set
      if (!serviceId || !templateId || !publicKey) {
        // Fallback for development - just log the data
        console.log('EmailJS not configured. Form data:', data)
        console.log('To set up EmailJS, see EMAILJS_SETUP.md')

        // Simulate delay for better UX
        await new Promise((resolve) => setTimeout(resolve, 1000))
      } else {
        // Template parameters for EmailJS
        const templateParams = {
          from_email: data.email,
          message: data.message,
          to_email: profile.email, // Your email where you want to receive messages
        }

        // Send email using EmailJS
        await emailjs.send(serviceId, templateId, templateParams, publicKey)
      }

      // Success
      setSubmitSuccess(true)
      reset()

      // Reset success message after 5 seconds
      setTimeout(() => setSubmitSuccess(false), 5000)
    } catch (err) {
      console.error("Error submitting form:", err)
      setSubmitError(true)
    } finally {
      setIsSubmitting(false)
    }
  }

  const contactInfo = [
    {
      icon: <Mail className="h-6 w-6 text-blue-500" />,
      title: "Email",
      value: profile.email,
      link: `mailto:${profile.email}`,
      description: "Feel free to email me anytime",
    },
    {
      icon: <Phone className="h-6 w-6 text-green-500" />,
      title: "Phone",
      value: profile.phone,
      link: `tel:${profile.phone}`,
      description: "Available for calls during business hours.",
    },
  ]

  return (
    <div className="min-h-screen p-8 md:p-12 bg-gradient-to-br from-blue-50 to-cyan-100 dark:from-gray-900 dark:to-blue-950">
      <div className="max-w-5xl mx-auto">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-4xl md:text-5xl font-bold mb-6 text-center lg:text-left"
        >
          Get in <span className="text-blue-600 dark:text-blue-400">Touch</span>
        </motion.h2>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-lg text-muted-foreground mb-12 text-center lg:text-left"
        >
          Have a project in mind or want to chat? I&apos;d love to hear from you!
        </motion.p>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 md:p-8 rounded-xl shadow-lg">
              <h3 className="text-2xl font-semibold mb-6">Send a Message</h3>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium mb-2">
                    Email
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    {...register("email")}
                    className={errors.email ? "border-destructive" : ""}
                  />
                  {errors.email && <p className="mt-1 text-sm text-destructive">{errors.email.message}</p>}
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium mb-2">
                    Message
                  </label>
                  <Textarea
                    id="message"
                    placeholder="Your message"
                    rows={8}
                    {...register("message")}
                    className={errors.message ? "border-destructive" : ""}
                  />
                  {errors.message && <p className="mt-1 text-sm text-destructive">{errors.message.message}</p>}
                </div>

                <Button type="submit" disabled={isSubmitting} className="w-full rounded-full">
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Send Message
                    </>
                  )}
                </Button>

                {submitSuccess && (
                  <div className="p-4 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-md">
                    ✅ Your message has been sent successfully! I&apos;ll get back to you soon.
                  </div>
                )}

                {submitError && (
                  <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-md">
                    ❌ There was an error sending your message. Please try again or contact me directly via email.
                  </div>
                )}
              </form>
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="space-y-8"
          >
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 md:p-8 rounded-xl shadow-lg">
              <h3 className="text-2xl font-semibold mb-6">Contact Information</h3>

              <div className="space-y-8">
                {contactInfo.map((info, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                    className="flex items-start"
                  >
                    <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/50 mr-4">{info.icon}</div>
                    <div>
                      <h4 className="font-medium text-lg">{info.title}</h4>
                      {info.link ? (
                        <a href={info.link} className="text-blue-600 dark:text-blue-400 hover:underline font-medium">
                          {info.value}
                        </a>
                      ) : (
                        <p className="font-medium">{info.value}</p>
                      )}
                      <p className="text-muted-foreground mt-1">{info.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>

              <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h4 className="font-medium text-lg mb-4">Connect With Me</h4>
                <div className="flex flex-wrap gap-4">
                  {socialLinks.map((link) => (
                    <a
                      key={link.id}
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/50 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors"
                      aria-label={link.name}
                    >
                      {link.icon}
                    </a>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

