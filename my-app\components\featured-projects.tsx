"use client"

import { motion } from "framer-motion"
import { SectionHeader } from "./section-header"
import ProjectCard from "./project-card"
import { Button } from "./ui/button"
import Link from "next/link"
import { ArrowRight } from "lucide-react"

const featuredProjects = [
  {
    id: 1,
    title: "E-Commerce Mobile App",
    description:
      "A cross-platform mobile application built with React Native and Expo, featuring a clean UI and seamless shopping experience.",
    image: "/placeholder.svg?height=600&width=800",
    tags: ["React Native", "Expo", "Redux", "Firebase"],
    demoUrl: "#",
    codeUrl: "#",
  },
  {
    id: 2,
    title: "Portfolio Website",
    description:
      "A modern, responsive portfolio website built with Next.js and Tailwind CSS, showcasing projects and skills.",
    image: "/placeholder.svg?height=600&width=800",
    tags: ["Next.js", "Tailwind CSS", "Framer Motion"],
    demoUrl: "#",
    codeUrl: "#",
  },
  {
    id: 3,
    title: "Task Management Dashboard",
    description:
      "A comprehensive task management application with real-time updates, user authentication, and team collaboration features.",
    image: "/placeholder.svg?height=600&width=800",
    tags: ["React", "Node.js", "MongoDB", "Socket.io"],
    demoUrl: "#",
    codeUrl: "#",
  },
]

export default function FeaturedProjects() {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <SectionHeader title="Featured Projects" description="Some of my recent work" />

        <div className="mt-12 space-y-12">
          {featuredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <ProjectCard project={project} featured />
            </motion.div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <Button asChild size="lg" className="rounded-full">
            <Link href="/portfolio">
              View All Projects
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}

