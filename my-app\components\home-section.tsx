"use client"

import { motion } from "framer-motion"
import { ArrowRight } from "lucide-react"
import { Button } from "@/components/ui/button"

// Static profile data
const profile = {
  name: "Yessine JAOUA",
  title: "Web & Mobile Developer",
  bio : "Passionate Web & Mobile Developer specializing in cross-platform apps with React Native and Jetpack Compose. Experienced in building SaaS web apps using React, Next.js, and a modern backend stack: Supabase, NestJS, Drizzle ORM. Skilled with Redux Toolkit Query, React Query, and React Hook Form for state and form management."
}

export default function HomeSection() {

  // Remove the unused variable
  // const isDark = mounted && resolvedTheme === "dark"

  return (
    <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-violet-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-950">
      <div className="max-w-3xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center lg:text-left"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="mb-6 inline-block"
          >
            <div className="text-sm font-medium px-4 py-2 rounded-full bg-violet-200 dark:bg-violet-900/50 text-violet-800 dark:text-violet-200">
              {profile.title}
            </div>
          </motion.div>

          <motion.h1
            className="text-5xl md:text-7xl font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            Creating digital{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-violet-600 to-indigo-600 dark:from-violet-400 dark:to-indigo-400">
              experiences
            </span>{" "}
            that matter
          </motion.h1>

          <motion.p
            className="text-xl text-muted-foreground mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            {profile.bio}
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
            className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <Button
              size="lg"
              className="rounded-full group"
              onClick={() => {
                // Update URL and trigger section change
                window.history.pushState({}, "", "#portfolio")

                // For mobile view, scroll to portfolio section
                if (window.innerWidth < 1024) {
                  const portfolioSection = document.getElementById("portfolio")
                  if (portfolioSection) {
                    portfolioSection.scrollIntoView({ behavior: "smooth" })
                  }
                } else {
                  // For desktop view, trigger a custom event to change section
                  window.dispatchEvent(new CustomEvent('navigate-to-section', {
                    detail: { section: 'portfolio' }
                  }))
                }
              }}
            >
              View My Work
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="rounded-full"
              onClick={() => {
                const contactSection = document.getElementById("contact")
                if (contactSection) {
                  contactSection.scrollIntoView({ behavior: "smooth" })
                }
              }}
            >
              Get in Touch
            </Button>
          </motion.div>
        </motion.div>

        {/* Animated background elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <motion.div
            className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-violet-400/20 dark:bg-violet-700/20 blur-3xl"
            animate={{
              x: [0, 30, 0],
              y: [0, -30, 0],
            }}
            transition={{
              repeat: Number.POSITIVE_INFINITY,
              duration: 8,
              ease: "easeInOut",
            }}
          />
          <motion.div
            className="absolute bottom-1/4 left-1/4 w-64 h-64 rounded-full bg-indigo-400/20 dark:bg-indigo-700/20 blur-3xl"
            animate={{
              x: [0, -30, 0],
              y: [0, 30, 0],
            }}
            transition={{
              repeat: Number.POSITIVE_INFINITY,
              duration: 10,
              ease: "easeInOut",
            }}
          />
        </div>
      </div>
    </div>
  )
}

