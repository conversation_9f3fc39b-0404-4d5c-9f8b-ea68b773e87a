"use client"

import { GraduationCap, Calendar, MapPin } from "lucide-react"
import Image from "next/image"
import AnimatedSectionHeader from "./AnimatedSectionHeader"
import { motion } from "framer-motion"

export default function Education() {
  const education = [
    {
      institution: "Ecole Nationale Des Sciences Appliquées Marrakech",
      location: "Maroc",
      details: [
        {
          degree: "Génie Informatique",
          period: "2020 – 2022",
        },
        {
          degree: "Classes Préparatoires",
          period: "2018 – 2020",
        },
      ],
      note: "Boursier De L'État Tunisien",
    },
    {
      institution: "Lycée Pilote Sfax",
      location: "Tunisie",
      details: [
        {
          degree: "Baccalauréat Math Mention Bien",
          period: "2017 – 2018",
        },
      ],
    },
  ]

  const languages = [
    { language: "Français", level: "DELF B2" },
    { language: "Arabe", level: "Maternelle" },
    { language: "Anglais", level: "Courant" },
  ]

  const associativeLife = [
    {
      organization: "Club Génie Informatique",
      period: "2020 - 2022",
      roles: ["Chef Projet (ColocaKech)", "Se<PERSON>rétaire général"],
    },
    {
      organization: "Hult Prize Ensa Marrakech",
      period: "2020 - 2022",
      roles: ["Campus Director", "Membre Organization & Regional Summit Competitor (Tunis Regionals)"],
    },
  ]

  return (
    <section
      id="education"
      className="py-20 bg-gradient-to-br from-indigo-50 to-purple-100 dark:from-gray-900 dark:to-purple-900 transition-colors duration-300 overflow-hidden relative"
    >
      <div className="container mx-auto px-6 relative z-10">
        <AnimatedSectionHeader title="Éducation" />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className="space-y-8">
            {education.map((edu, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg transition-all duration-300 hover:shadow-2xl relative overflow-hidden"
              >
                <div className="absolute top-0 left-0 w-32 h-32 bg-purple-200 dark:bg-purple-700 rounded-br-full z-0 opacity-50"></div>
                <div className="relative z-10">
                  <h3 className="text-2xl font-semibold mb-2 dark:text-white flex items-center">
                    <GraduationCap className="w-6 h-6 mr-2" />
                    {edu.institution}
                  </h3>

                  <p className="text-gray-600 dark:text-gray-300 mb-4 flex items-center">
                    <MapPin className="w-4 h-4 mr-2" />
                    {edu.location}
                  </p>

                  {edu.note && <p className="text-blue-600 dark:text-blue-400 mb-4 italic">{edu.note}</p>}

                  <div className="space-y-4">
                    {edu.details.map((detail, detailIndex) => (
                      <div key={detailIndex}>
                        <p className="text-xl text-gray-700 dark:text-gray-200 font-medium">{detail.degree}</p>
                        <p className="text-gray-600 dark:text-gray-300 flex items-center">
                          <Calendar className="w-4 h-4 mr-2" />
                          {detail.period}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="space-y-8">
            {/* Languages Section */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg transition-all duration-300 hover:shadow-2xl"
            >
              <h3 className="text-2xl font-semibold mb-6 dark:text-white">Langues</h3>
              <div className="space-y-4">
                {languages.map((lang, langIndex) => (
                  <div key={langIndex} className="flex justify-between items-center">
                    <span className="text-lg text-gray-700 dark:text-gray-300">{lang.language}</span>
                    <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full text-sm">
                      {lang.level}
                    </span>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Associative Life Section */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg transition-all duration-300 hover:shadow-2xl"
            >
              <h3 className="text-2xl font-semibold mb-6 dark:text-white">Vie Associative</h3>
              <div className="space-y-6">
                {associativeLife.map((assoc, assocIndex) => (
                  <div key={assocIndex}>
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="text-lg font-medium text-gray-700 dark:text-gray-200">{assoc.organization}</h4>
                      <span className="text-sm text-gray-500 dark:text-gray-400">{assoc.period}</span>
                    </div>
                    <ul className="list-disc list-inside space-y-1 pl-2">
                      {assoc.roles.map((role, roleIndex) => (
                        <li key={roleIndex} className="text-gray-600 dark:text-gray-300 text-sm">
                          {role}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
      <div className="absolute top-0 left-0 w-64 h-64 -mt-32 -ml-32 opacity-20">
        <Image src="/placeholder.svg?height=256&width=256" alt="Decorative background" width={256} height={256} />
      </div>
    </section>
  )
}

