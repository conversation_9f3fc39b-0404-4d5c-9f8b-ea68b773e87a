import Image from "next/image"
import Link from "next/link"
import { Badge } from "./ui/badge"
import { Calendar, Clock } from "lucide-react"

interface BlogPost {
  id: number
  title: string
  excerpt: string
  image: string
  category: string
  date: string
  readTime: string
  slug: string
}

interface BlogPostCardProps {
  post: BlogPost
}

export default function BlogPostCard({ post }: BlogPostCardProps) {
  return (
    <div className="bg-background rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow">
      <div className="relative aspect-video">
        <Image src={post.image || "/placeholder.svg"} alt={post.title} fill className="object-cover" />
      </div>
      <div className="p-6 space-y-4">
        <Badge variant="secondary" className="mb-2">
          {post.category}
        </Badge>
        <h3 className="text-xl font-semibold">
          <Link href={`/blog/${post.slug}`} className="hover:text-primary transition-colors">
            {post.title}
          </Link>
        </h3>
        <p className="text-muted-foreground line-clamp-2">{post.excerpt}</p>
        <div className="flex items-center gap-4 text-sm text-muted-foreground pt-2">
          <div className="flex items-center">
            <Calendar className="mr-1 h-4 w-4" />
            {post.date}
          </div>
          <div className="flex items-center">
            <Clock className="mr-1 h-4 w-4" />
            {post.readTime}
          </div>
        </div>
        <div className="pt-2">
          <Link
            href={`/blog/${post.slug}`}
            className="text-primary hover:text-primary/80 font-medium transition-colors"
          >
            Read More
          </Link>
        </div>
      </div>
    </div>
  )
}

