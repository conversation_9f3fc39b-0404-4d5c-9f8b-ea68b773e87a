"use client"

import { useState, useEffect, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Sidebar from "./sidebar"
import HomeSection from "@/components/home-section"
import AboutSection from "@/components/about-section"
import PortfolioSection from "@/components/portfolio-section"
import AwardsSection from "@/components/awards-section"
import BlogSection from "@/components/blog-section"
import ContactSection from "@/components/contact-section"
import { ModeToggle } from "./mode-toggle"

// Define all available sections
export type SectionType = "home" | "about" | "portfolio" | "awards" | "blog" | "contact" | "admin"

export default function SplitLayout() {
  const [mounted, setMounted] = useState(false)
  const [activeSection, setActiveSection] = useState<SectionType>("home")
  const [isScrolling, setIsScrolling] = useState(false)
  const [isAdmin, setIsAdmin] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Secret admin mode with Ctrl+Shift+A
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.ctrlKey && e.shiftKey && e.key === "A") {
      setIsAdmin(true)
      setActiveSection("admin")
    }
  }, [])

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [handleKeyDown])

  // Listen for custom navigation events
  useEffect(() => {
    const handleNavigateToSection = (event: CustomEvent) => {
      const { section } = event.detail
      if (["home", "about", "portfolio", "awards", "blog", "contact"].includes(section)) {
        setActiveSection(section as SectionType)
      }
    }

    window.addEventListener('navigate-to-section', handleNavigateToSection as EventListener)
    return () => window.removeEventListener('navigate-to-section', handleNavigateToSection as EventListener)
  }, [])

  // Handle section change from sidebar
  const handleSectionChange = (section: SectionType) => {
    setActiveSection(section)

    // Update URL without page reload
    window.history.pushState({}, "", `#${section}`)

    // Scroll to section for mobile view
    const element = document.getElementById(section)
    if (element && window.innerWidth < 1024) {
      setIsScrolling(true)
      element.scrollIntoView({ behavior: "smooth" })
      setTimeout(() => setIsScrolling(false), 1000)
    }
  }

  // Handle scroll for mobile view
  useEffect(() => {
    const handleScroll = () => {
      if (isScrolling || window.innerWidth >= 1024) return

      const sections = ["home", "about", "portfolio", "awards", "blog", "contact"]

      for (const section of sections) {
        const element = document.getElementById(section)
        if (!element) continue

        const rect = element.getBoundingClientRect()
        if (rect.top <= 100 && rect.bottom >= 100) {
          setActiveSection(section as SectionType)
          window.history.replaceState({}, "", `#${section}`)
          break
        }
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [isScrolling])

  // Check URL hash on initial load
  useEffect(() => {
    const hash = window.location.hash.replace("#", "")
    if (hash && ["home", "about", "portfolio", "awards", "blog", "contact", "admin"].includes(hash)) {
      setActiveSection(hash as SectionType)
    }
  }, [])

  if (!mounted) {
    return null // Prevent rendering until client-side
  }

  return (
    <div className="flex flex-col lg:flex-row min-h-screen bg-background">
      {/* Fixed sidebar for desktop, top navigation for mobile */}
      <Sidebar activeSection={activeSection} onSectionChange={handleSectionChange} showAdmin={isAdmin} />

      {/* Theme toggle button */}
      <div className="fixed top-20 right-4 lg:top-4 lg:right-4 z-40">
        <ModeToggle />
      </div>

      {/* Main content area */}
      <main className="flex-1 lg:ml-[35vw]">
        {/* Desktop view with animated transitions */}
        <div className="hidden lg:block h-screen overflow-hidden">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeSection}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="h-full overflow-y-auto scrollbar-thin"
            >
              {renderSection(activeSection)}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Mobile view with all sections stacked */}
        <div className="block lg:hidden">
          {/* Mobile view sections */}
          <section id="home" className="min-h-screen">
            <HomeSection />
          </section>
          <section id="about" className="min-h-screen">
            <AboutSection />
          </section>
          <section id="portfolio" className="min-h-screen">
            <PortfolioSection />
          </section>
          <section id="awards" className="min-h-screen">
            <AwardsSection />
          </section>
          <section id="blog" className="min-h-screen">
            <BlogSection />
          </section>
          <section id="contact" className="min-h-screen">
            <ContactSection />
          </section>
        </div>
      </main>
    </div>
  )
}

// Helper function to render the active section
function renderSection(section: SectionType) {
  switch (section) {
    case "home":
      return <HomeSection />
    case "about":
      return <AboutSection />
    case "portfolio":
      return <PortfolioSection />
    case "awards":
      return <AwardsSection />
    case "blog":
      return <BlogSection />
    case "contact":
      return <ContactSection />
    default:
      return <HomeSection />
  }
}

