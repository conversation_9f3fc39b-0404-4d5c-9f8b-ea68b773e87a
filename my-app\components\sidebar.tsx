"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Menu, X, Home, User, Briefcase, Award, BookOpen, Mail, BarChart3 } from "lucide-react"
import { <PERSON><PERSON> } from "./ui/button"
import { cn } from "@/lib/utils"
import type { SectionType } from "./split-layout"
import Image from "next/image"
import SocialLinks from "./social-links"

// Static profile data
const profile = {
  name: "Yessine JAOUA",
  title: "Web & Mobile Developer",
  avatar_url: "/assets/profile-image.jpg",
}

interface SidebarProps {
  activeSection: SectionType
  onSectionChange: (section: SectionType) => void
  showAdmin?: boolean
}

export default function Sidebar({ activeSection, onSectionChange, showAdmin = false }: SidebarProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const navItems = [
    { id: "home", label: "Home", icon: <Home className="w-5 h-5" /> },
    { id: "about", label: "About", icon: <User className="w-5 h-5" /> },
    { id: "portfolio", label: "Portfolio", icon: <Briefcase className="w-5 h-5" /> },
    { id: "awards", label: "Awards", icon: <Award className="w-5 h-5" /> },
    { id: "blog", label: "Blog", icon: <BookOpen className="w-5 h-5" /> },
    { id: "contact", label: "Contact", icon: <Mail className="w-5 h-5" /> },
  ]

  // Add admin section if enabled
  if (showAdmin) {
    navItems.push({ id: "admin", label: "Admin", icon: <BarChart3 className="w-5 h-5" /> })
  }

  // Get background color based on active section
  const getBackgroundColor = () => {
    if (!mounted) return ""

    switch (activeSection) {
      case "home":
        return "bg-gradient-to-br from-violet-500 to-indigo-600 dark:from-violet-900 dark:to-indigo-950"
      case "about":
        return "bg-gradient-to-br from-emerald-500 to-teal-600 dark:from-emerald-900 dark:to-teal-950"
      case "portfolio":
        return "bg-gradient-to-br from-amber-500 to-orange-600 dark:from-amber-900 dark:to-orange-950"
      case "awards":
        return "bg-gradient-to-br from-yellow-500 to-amber-600 dark:from-yellow-900 dark:to-amber-950"
      case "blog":
        return "bg-gradient-to-br from-rose-500 to-pink-600 dark:from-rose-900 dark:to-pink-950"
      case "contact":
        return "bg-gradient-to-br from-blue-500 to-cyan-600 dark:from-blue-900 dark:to-cyan-950"
      case "admin":
        return "bg-gradient-to-br from-gray-500 to-slate-600 dark:from-gray-900 dark:to-slate-950"
      default:
        return "bg-gradient-to-br from-violet-500 to-indigo-600 dark:from-violet-900 dark:to-indigo-950"
    }
  }

  return (
    <>
      {/* Mobile Header */}
      <header className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-md border-b border-border shadow-sm">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full overflow-hidden">
              <Image
                src={profile.avatar_url || "/placeholder.svg"}
                alt={profile.name}
                width={40}
                height={40}
                className="object-cover"
              />
            </div>
            <span className="font-bold text-lg">{profile.name}</span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle menu"
            className="hover:bg-muted/80 focus:bg-muted/80 transition-colors duration-200"
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6 text-foreground" />
            ) : (
              <Menu className="h-6 w-6 text-foreground" />
            )}
          </Button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="border-t border-border bg-background/95 backdrop-blur-md"
          >
            <nav className="p-4 flex flex-col space-y-2">
              {navItems.map((item) => (
                <Button
                  key={item.id}
                  variant="ghost"
                  className={cn(
                    "justify-start h-12 text-base font-medium transition-colors duration-200",
                    activeSection === item.id
                      ? "bg-primary/10 text-primary border border-primary/20"
                      : "hover:bg-muted/80"
                  )}
                  onClick={() => {
                    onSectionChange(item.id as SectionType)
                    setIsMobileMenuOpen(false)
                  }}
                >
                  <div className="flex items-center">
                    {item.icon}
                    <span className="ml-3">{item.label}</span>
                  </div>
                </Button>
              ))}
            </nav>
          </motion.div>
        )}
      </header>

      {/* Desktop Sidebar */}
      <aside
        className={cn(
          "fixed top-0 left-0 bottom-0 w-[35vw] hidden lg:flex flex-col transition-colors duration-500 ease-in-out z-30",
          getBackgroundColor(),
        )}
      >
        <div className="flex flex-col justify-between h-full p-12 text-white">
          {/* Profile Section */}
          <div className="space-y-8">
            <div className="relative w-32 h-32 mx-auto">
              <div className="absolute inset-0 rounded-full bg-white/20 blur-md"></div>
              <div className="absolute inset-1 rounded-full overflow-hidden">
                <Image
                  src={profile.avatar_url || "/placeholder.svg"}
                  alt={profile.name}
                  fill
                  className="object-cover"
                />
              </div>
            </div>

            <div className="text-center">
              <h1 className="text-3xl font-bold">{profile.name}</h1>
              <p className="text-white/80 mt-2">{profile.title}</p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="space-y-6">
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => onSectionChange(item.id as SectionType)}
                className={cn(
                  "flex items-center w-full px-6 py-3 rounded-lg transition-all duration-300",
                  activeSection === item.id
                    ? "bg-white/20 backdrop-blur-sm shadow-lg transform translate-x-2"
                    : "hover:bg-white/10 hover:translate-x-1",
                )}
              >
                <div className="flex items-center">
                  {item.icon}
                  <span className="ml-3 text-lg font-medium">{item.label}</span>
                </div>
                {activeSection === item.id && (
                  <motion.div
                    layoutId="activeIndicator"
                    className="ml-auto w-2 h-2 rounded-full bg-white"
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  />
                )}
              </button>
            ))}
          </nav>

          {/* Social Links */}
          <div className="text-center">
            <SocialLinks className="justify-center" />
            <p className="mt-4 text-sm text-white/70">
              © {new Date().getFullYear()} {profile.name}
            </p>
          </div>
        </div>
      </aside>

      {/* Padding for mobile view to account for fixed header */}
      <div className="h-16 lg:hidden"></div>
    </>
  )
}

