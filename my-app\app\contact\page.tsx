import type { Metadata } from "next"
import PageHeader from "@/components/page-header"
import ContactForm from "@/components/contact-form"
import SocialLinks from "@/components/social-links"

export const metadata: Metadata = {
  title: "Contact | <PERSON>",
  description: "Get in touch with <PERSON> for collaboration, questions, or opportunities",
}

export default function ContactPage() {
  return (
    <div className="container mx-auto px-4 py-16">
      <PageHeader
        title="Get in Touch"
        description="Have a project in mind or want to collaborate? I'd love to hear from you."
      />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mt-12">
        <ContactForm />
        <div className="space-y-8">
          <div>
            <h3 className="text-xl font-semibold mb-4">Contact Information</h3>
            <p className="text-muted-foreground mb-2">Email: <EMAIL></p>
            <p className="text-muted-foreground">Phone: +****************</p>
          </div>
          <div>
            <h3 className="text-xl font-semibold mb-4">Connect With Me</h3>
            <SocialLinks className="flex gap-4" />
          </div>
        </div>
      </div>
    </div>
  )
}

