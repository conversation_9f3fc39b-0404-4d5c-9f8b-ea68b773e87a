"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { Calendar, Clock, ArrowRight } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

// Updated blog posts data with your Medium articles
const blogPosts = [
  {
    id: 1,
    title: "Lessons Learned: Navigating the Transition from Student to Mobile Developer—Part 2",
    excerpt:
      "Continuing the journey of transitioning from student to professional mobile developer with more insights and practical advice.",
    image_url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-pp1q3fydhMEtKSj59XryS7cbodunc1.png",
    category: "Career Development",
    date: "November 21, 2024",
    read_time: "5 min read",
    slug: "lessons-learned-part-2",
    url: "https://medium.com/@yessinejawa/lessons-learned-navigating-the-transition-from-student-to-mobile-developer-part-2",
  },
  {
    id: 2,
    title: "Lessons Learned: Navigating the Transition from Student to Mobile Developer ⚡",
    excerpt:
      "Sharing my personal journey and key insights from transitioning from a student to a professional mobile developer.",
    image_url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-pp1q3fydhMEtKSj59XryS7cbodunc1.png",
    category: "Career Development",
    date: "April 9, 2024",
    read_time: "6 min read",
    slug: "lessons-learned-part-1",
    url: "https://medium.com/@yessinejawa/lessons-learned-navigating-the-transition-from-student-to-mobile-developer",
  },
]

// Function to escape apostrophes in text
const escapeApostrophes = (text: string): string => {
  return text.replace(/'/g, "&apos;")
}

export default function BlogSection() {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  }

  return (
    <div className="min-h-screen p-8 md:p-12 bg-gradient-to-br from-rose-50 to-pink-100 dark:from-gray-900 dark:to-rose-950">
      <div className="max-w-5xl mx-auto">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-4xl md:text-5xl font-bold mb-6 text-center lg:text-left"
        >
          My <span className="text-rose-600 dark:text-rose-400">Blog</span>
        </motion.h2>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-lg text-muted-foreground mb-12 text-center lg:text-left"
        >
          Thoughts, tutorials, and insights on mobile development and career growth
        </motion.p>

        <motion.div variants={container} initial="hidden" animate="show" className="space-y-8">
          {blogPosts.map((post) => (
            <motion.div
              key={post.id}
              variants={item}
              className="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/3 relative">
                  <div className="aspect-video md:aspect-square relative">
                    <Image
                      src={post.image_url || "https://images.pexels.com/photos/839443/pexels-photo-839443.jpeg?auto=compress&cs=tinysrgb&w=600"}
                      alt={post.title}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                </div>

                <div className="md:w-2/3 p-6 md:p-8">
                  <Badge className="mb-4 bg-rose-100 dark:bg-rose-900 text-rose-800 dark:text-rose-200">
                    {post.category}
                  </Badge>

                  <h3 className="text-xl md:text-2xl font-semibold mb-3 group-hover:text-rose-600 dark:group-hover:text-rose-400 transition-colors">
                    {escapeApostrophes(post.title)}
                  </h3>

                  <p className="text-muted-foreground mb-4">{escapeApostrophes(post.excerpt)}</p>

                  <div className="flex items-center text-sm text-muted-foreground mb-4">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span className="mr-4">{post.date}</span>
                    <Clock className="h-4 w-4 mr-1" />
                    <span>{post.read_time}</span>
                  </div>

                  <Button
                    variant="link"
                    className="p-0 h-auto text-rose-600 dark:text-rose-400 group-hover:underline"
                    onClick={() => window.open(post.url, "_blank")}
                  >
                    Read More
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-12 text-center"
        >
          <Button
            variant="outline"
            size="lg"
            className="rounded-full group"
            onClick={() => window.open("https://medium.com/@yessinejawa", "_blank")}
          >
            View All Articles
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </motion.div>
      </div>
    </div>
  )
}

