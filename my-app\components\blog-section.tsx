"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Image from "next/image"
import { Calendar, Clock, ArrowRight, FileText, ExternalLink, BookOpen, Newspaper } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"

// External blog posts data (Medium articles)
const externalBlogPosts = [
  {
    id: 1,
    title: "Lessons Learned: Navigating the Transition from Student to Mobile Developer—Part 2",
    excerpt:
      "Continuing the journey of transitioning from student to professional mobile developer with more insights and practical advice.",
    image_url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-pp1q3fydhMEtKSj59XryS7cbodunc1.png",
    category: "Career Development",
    date: "November 21, 2024",
    read_time: "5 min read",
    slug: "lessons-learned-part-2",
    url: "https://medium.com/@yessinejawa/lessons-learned-navigating-the-transition-from-student-to-mobile-developer-part-2",
    type: "external"
  },
  {
    id: 2,
    title: "Lessons Learned: Navigating the Transition from Student to Mobile Developer ⚡",
    excerpt:
      "Sharing my personal journey and key insights from transitioning from a student to a professional mobile developer.",
    image_url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-pp1q3fydhMEtKSj59XryS7cbodunc1.png",
    category: "Career Development",
    date: "April 9, 2024",
    read_time: "6 min read",
    slug: "lessons-learned-part-1",
    url: "https://medium.com/@yessinejawa/lessons-learned-navigating-the-transition-from-student-to-mobile-developer",
    type: "external"
  },
]

// Local HTML articles data (will be populated when you add HTML files)
const localArticles = [
  // Sample article to demonstrate functionality
  {
    id: 1,
    title: "Building Scalable React Native Apps",
    excerpt: "Learn the key principles and best practices for creating maintainable and performant React Native applications that can scale with your business needs.",
    category: "Technology",
    date: "January 15, 2025",
    read_time: "8 min read",
    htmlFile: "sample-article.html", // filename in public folder
    tags: ["React Native", "Architecture", "Performance", "Best Practices"]
  }
  // Add your actual articles here following the same structure
]

// Function to escape apostrophes in text
const escapeApostrophes = (text: string): string => {
  return text.replace(/'/g, "&apos;")
}

// Component for rendering external blog posts
const ExternalBlogCard = ({ post }: { post: any }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
  >
    <div className="flex flex-col md:flex-row">
      <div className="md:w-1/3 relative">
        <div className="aspect-video md:aspect-square relative">
          <Image
            src={post.image_url || "https://images.pexels.com/photos/839443/pexels-photo-839443.jpeg?auto=compress&cs=tinysrgb&w=600"}
            alt={post.title}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-105"
          />
        </div>
      </div>

      <div className="md:w-2/3 p-6 md:p-8">
        <div className="flex items-center gap-2 mb-4">
          <Badge className="bg-rose-100 dark:bg-rose-900 text-rose-800 dark:text-rose-200">
            {post.category}
          </Badge>
          <Badge variant="outline" className="text-xs">
            <ExternalLink className="h-3 w-3 mr-1" />
            Medium
          </Badge>
        </div>

        <h3 className="text-xl md:text-2xl font-semibold mb-3 group-hover:text-rose-600 dark:group-hover:text-rose-400 transition-colors">
          {escapeApostrophes(post.title)}
        </h3>

        <p className="text-muted-foreground mb-4">{escapeApostrophes(post.excerpt)}</p>

        <div className="flex items-center text-sm text-muted-foreground mb-4">
          <Calendar className="h-4 w-4 mr-1" />
          <span className="mr-4">{post.date}</span>
          <Clock className="h-4 w-4 mr-1" />
          <span>{post.read_time}</span>
        </div>

        <Button
          variant="link"
          className="p-0 h-auto text-rose-600 dark:text-rose-400 group-hover:underline"
          onClick={() => window.open(post.url, "_blank")}
        >
          Read More
          <ArrowRight className="ml-1 h-4 w-4" />
        </Button>
      </div>
    </div>
  </motion.div>
)

// Component for rendering local HTML articles
const LocalArticleCard = ({ article }: { article: any }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="group bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700"
  >
    <div className="flex items-start justify-between mb-4">
      <div className="flex items-center gap-2">
        <div className="p-2 bg-rose-100 dark:bg-rose-900 rounded-lg">
          <FileText className="h-5 w-5 text-rose-600 dark:text-rose-400" />
        </div>
        <Badge className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
          {article.category}
        </Badge>
      </div>
      <Badge variant="outline" className="text-xs">
        <BookOpen className="h-3 w-3 mr-1" />
        Original
      </Badge>
    </div>

    <h3 className="text-xl font-semibold mb-3 group-hover:text-rose-600 dark:group-hover:text-rose-400 transition-colors">
      {article.title}
    </h3>

    <p className="text-muted-foreground mb-4 line-clamp-3">{article.excerpt}</p>

    {article.tags && (
      <div className="flex flex-wrap gap-2 mb-4">
        {article.tags.map((tag: string, index: number) => (
          <span
            key={index}
            className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full"
          >
            {tag}
          </span>
        ))}
      </div>
    )}

    <div className="flex items-center justify-between">
      <div className="flex items-center text-sm text-muted-foreground">
        <Calendar className="h-4 w-4 mr-1" />
        <span className="mr-4">{article.date}</span>
        <Clock className="h-4 w-4 mr-1" />
        <span>{article.read_time}</span>
      </div>

      <Button
        variant="link"
        className="p-0 h-auto text-rose-600 dark:text-rose-400 group-hover:underline"
        onClick={() => window.open(`/${article.htmlFile}`, "_blank")}
      >
        Read Article
        <ArrowRight className="ml-1 h-4 w-4" />
      </Button>
    </div>
  </motion.div>
)

export default function BlogSection() {
  const [activeTab, setActiveTab] = useState("published")

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  return (
    <div className="min-h-screen p-8 md:p-12 bg-gradient-to-br from-rose-50 to-pink-100 dark:from-gray-900 dark:to-rose-950">
      <div className="max-w-6xl mx-auto">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-4xl md:text-5xl font-bold mb-6 text-center lg:text-left"
        >
          My <span className="text-rose-600 dark:text-rose-400">Blog</span>
        </motion.h2>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-lg text-muted-foreground mb-8 text-center lg:text-left"
        >
          Thoughts, tutorials, and insights on mobile development and career growth
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-8"
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 lg:w-fit lg:grid-cols-2 mb-8">
              <TabsTrigger value="published" className="flex items-center gap-2">
                <Newspaper className="h-4 w-4" />
                Published Articles
              </TabsTrigger>
              <TabsTrigger value="original" className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Original Content
              </TabsTrigger>
            </TabsList>

            <AnimatePresence mode="wait">
              <TabsContent value="published" className="mt-0">
                <motion.div
                  key="published"
                  variants={container}
                  initial="hidden"
                  animate="show"
                  exit="hidden"
                  className="space-y-8"
                >
                  {externalBlogPosts.map((post) => (
                    <ExternalBlogCard key={post.id} post={post} />
                  ))}
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  className="mt-12 text-center"
                >
                  <Button
                    variant="outline"
                    size="lg"
                    className="rounded-full group"
                    onClick={() => window.open("https://medium.com/@yessinejawa", "_blank")}
                  >
                    View All on Medium
                    <ExternalLink className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </motion.div>
              </TabsContent>

              <TabsContent value="original" className="mt-0">
                <motion.div
                  key="original"
                  variants={container}
                  initial="hidden"
                  animate="show"
                  exit="hidden"
                >
                  {localArticles.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {localArticles.map((article) => (
                        <LocalArticleCard key={article.id} article={article} />
                      ))}
                    </div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-center py-16"
                    >
                      <div className="max-w-md mx-auto">
                        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-full w-fit mx-auto mb-6">
                          <FileText className="h-12 w-12 text-gray-400" />
                        </div>
                        <h3 className="text-xl font-semibold mb-3 text-gray-700 dark:text-gray-300">
                          Original Content Coming Soon
                        </h3>
                        <p className="text-muted-foreground mb-6">
                          This section will showcase exclusive articles and tutorials written specifically for this portfolio.
                          Stay tuned for in-depth technical content and insights!
                        </p>
                        <div className="flex flex-wrap gap-2 justify-center">
                          <Badge variant="outline">React Native</Badge>
                          <Badge variant="outline">Mobile Development</Badge>
                          <Badge variant="outline">TypeScript</Badge>
                          <Badge variant="outline">Career Tips</Badge>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              </TabsContent>
            </AnimatePresence>
          </Tabs>
        </motion.div>
      </div>
    </div>
  )
}

