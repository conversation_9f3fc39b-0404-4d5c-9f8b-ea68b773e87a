"use client"

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ExternalLink, Calendar, Code, Target, Users, Zap, CheckCircle, Brain } from "lucide-react"

interface ProjectDetailsModalProps {
  project: {
    id: number
    title: string
    description: string
    tags: string[]
    demo_url: string
    code_url: string
    featured: boolean
    icon: string
    year: number
    status?: string
  }
  isOpen: boolean
  onClose: () => void
}

// Enhanced project data with additional details
interface ProjectDetails {
  duration: string
  teamSize: string
  challenges: string[]
  features: string[]
  achievements: string[]
  techStack: {
    frontend?: string[]
    backend?: string[]
    tools?: string[]
    deployment?: string[]
    database?: string[]
  }
  skills: string[]
}

const getProjectDetails = (projectId: number): Partial<ProjectDetails> => {
  const projectDetailsMap: Record<number, ProjectDetails> = {
    1: { // AI-Powered SEO Article Generator
      duration: "3 months",
      teamSize: "Solo Project",
      challenges: ["AI Integration", "SEO Optimization", "Scalable Architecture", "Performance Optimization"],
      features: ["AI Content Generation", "Keyword Optimization", "Multi-language Support", "Analytics Dashboard"],
      achievements: ["50% faster content creation", "90% SEO score improvement", "Multi-tenant architecture"],
      techStack: {
        frontend: ["React", "MUI", "Redux Toolkit Query"],
        backend: ["Pocketsflow", "VPS"],
        deployment: ["Vercel", "Hostinger"]
      },
      skills: ["Agile Development", "Self-management", "AI/ML Integration", "Full-stack Development"]
    },
    2: { // Real Estate Syndic Management App
      duration: "4 months",
      teamSize: "2 developers",
      challenges: ["Offline Functionality", "Document Management", "Payment Integration", "Team Coordination"],
      features: ["Payment Tracking", "Document Upload", "Push Notifications", "Multi-language"],
      achievements: ["100% offline capability", "40% faster payment processing", "99.9% uptime"],
      techStack: {
        frontend: ["React Native", "Redux"],
        backend: ["Firebase"],
        tools: ["Expo"]
      },
      skills: ["Agile Methodology", "Pair Programming", "Cross-platform Development", "Team Collaboration"]
    },
    3: { // Sales Facilitator App for Telecom
      duration: "6 months",
      teamSize: "3 developers",
      challenges: ["Offline OCR", "Modular Architecture", "Performance Optimization", "CI/CD Pipeline Setup"],
      features: ["Offline OCR", "Customer Registration", "Sales Analytics", "Multi-module Design", "Automated Testing"],
      achievements: ["60% faster field operations", "95% OCR accuracy", "Modular scalability", "Zero-downtime deployments"],
      techStack: {
        frontend: ["Jetpack Compose"],
        backend: ["Android Native"],
        tools: ["Offline OCR SDK", "Multi-Module Architecture", "Jenkins", "Maestro E2E Testing"]
      },
      skills: ["Agile Development", "Cross-functional Communication", "CI/CD Implementation", "Test Automation"]
    },
    4: { // Web-to-Mobile Migration
      duration: "2 months",
      teamSize: "Solo Project",
      challenges: ["Platform Adaptation", "Performance Optimization", "User Experience", "CI/CD Migration"],
      features: ["Cross-platform Compatibility", "Native Performance", "Responsive Design", "Automated Deployment"],
      achievements: ["100% feature parity", "50% better performance", "Enhanced UX", "Seamless CI/CD pipeline"],
      techStack: {
        frontend: ["React Native", "Redux"],
        tools: ["API Integration", "Azure DevOps", "CI/CD Pipelines"]
      },
      skills: ["Agile Methodology", "Self-directed Learning", "DevOps Practices", "Platform Migration"]
    },
    5: { // Student Housing Web App
      duration: "5 months",
      teamSize: "4 developers",
      challenges: ["Real-time Search", "Geolocation", "Payment Integration", "Team Coordination"],
      features: ["Advanced Filtering", "Map Integration", "Booking System", "User Reviews"],
      achievements: ["500+ active users", "95% booking success rate", "4.8/5 user rating"],
      techStack: {
        frontend: ["React"],
        backend: ["Node.js", "Express", "MongoDB"],
        database: ["MongoDB"]
      },
      skills: ["Agile Scrum", "Team Leadership", "Full-stack Development", "Stakeholder Communication"]
    },
    6: { // Budget Tracker Mobile App
      duration: "3 months",
      teamSize: "Solo Project",
      challenges: ["Offline Storage", "Data Synchronization", "User Experience", "Performance Optimization"],
      features: ["Offline-first", "Expense Categorization", "Budget Analytics", "Export Reports"],
      achievements: ["100% offline functionality", "1000+ downloads", "4.5/5 app rating"],
      techStack: {
        frontend: ["React Native"],
        database: ["SQLite"],
        tools: ["Offline Storage"]
      },
      skills: ["Agile Development", "Mobile Development", "Data Management", "User-centered Design"]
    },
    7: { // Event Organizer App
      duration: "4 months",
      teamSize: "3 developers",
      challenges: ["Real-time Matching", "Service Provider Network", "Event Management", "Cross-team Communication"],
      features: ["Service Provider Matching", "Event Planning", "Real-time Chat", "Payment Processing"],
      achievements: ["200+ service providers", "150+ successful events", "4.7/5 user rating"],
      techStack: {
        frontend: ["Flutter"],
        backend: ["Spring Boot", "Firebase"],
        tools: ["Dart"]
      },
      skills: ["Agile Methodology", "Cross-functional Teams", "Mobile Development", "Backend Integration"]
    }
  }

  return projectDetailsMap[projectId] || {}
}

export default function ProjectDetailsModal({ project, isOpen, onClose }: ProjectDetailsModalProps) {
  const details = getProjectDetails(project.id)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-2xl">
            <span className="text-3xl">{project.icon}</span>
            {project.title}
            {project.status && (
              <Badge variant="secondary" className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                {project.status}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-8">
          {/* Project Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Target className="h-5 w-5 text-amber-600" />
              Project Overview
            </h3>
            <p className="text-muted-foreground leading-relaxed">{project.description}</p>
          </motion.div>

          {/* Project Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-4"
          >
            <div className="bg-amber-50 dark:bg-amber-950 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-amber-600" />
                <span className="font-medium">Timeline</span>
              </div>
              <p className="text-sm text-muted-foreground">{details.duration || "N/A"}</p>
              <p className="text-xs text-muted-foreground mt-1">{project.year}</p>
            </div>
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Users className="h-4 w-4 text-blue-600" />
                <span className="font-medium">Team Size</span>
              </div>
              <p className="text-sm text-muted-foreground">{details.teamSize || "N/A"}</p>
            </div>
            <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Code className="h-4 w-4 text-green-600" />
                <span className="font-medium">Technologies</span>
              </div>
              <p className="text-sm text-muted-foreground">{project.tags.length} technologies</p>
            </div>
          </motion.div>

          {/* Technology Stack */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="space-y-4"
          >
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Code className="h-5 w-5 text-blue-600" />
              Technology Stack
            </h3>
            <div className="flex flex-wrap gap-2">
              {project.tags.map((tag) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                >
                  {tag}
                </Badge>
              ))}
            </div>

            {details.techStack && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                {details.techStack.frontend && (
                  <div>
                    <h4 className="font-medium text-sm mb-2">Frontend</h4>
                    <div className="space-y-1">
                      {details.techStack.frontend.map((tech: string) => (
                        <div key={tech} className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                          {tech}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                {details.techStack.backend && (
                  <div>
                    <h4 className="font-medium text-sm mb-2">Backend</h4>
                    <div className="space-y-1">
                      {details.techStack.backend.map((tech: string) => (
                        <div key={tech} className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                          {tech}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                {(details.techStack.tools || details.techStack.deployment) && (
                  <div>
                    <h4 className="font-medium text-sm mb-2">Tools & Deployment</h4>
                    <div className="space-y-1">
                      {(details.techStack.tools || details.techStack.deployment || []).map((tech: string) => (
                        <div key={tech} className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                          {tech}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </motion.div>

          {/* Key Features */}
          {details.features && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
              className="space-y-4"
            >
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Zap className="h-5 w-5 text-purple-600" />
                Key Features
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {details.features.map((feature: string, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Achievements */}
          {details.achievements && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
              className="space-y-4"
            >
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Key Achievements
              </h3>
              <div className="grid grid-cols-1 gap-2">
                {details.achievements.map((achievement: string, index: number) => (
                  <div key={index} className="flex items-center gap-2 bg-green-50 dark:bg-green-950 p-3 rounded-lg">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">{achievement}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Skills & Methodologies */}
          {details.skills && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
              className="space-y-4"
            >
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Brain className="h-5 w-5 text-indigo-600" />
                Skills & Methodologies
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {details.skills.map((skill: string, index: number) => (
                  <div key={index} className="bg-indigo-50 dark:bg-indigo-950 p-3 rounded-lg text-center">
                    <span className="text-sm font-medium text-indigo-800 dark:text-indigo-200">{skill}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.6 }}
            className="flex gap-4 pt-4 border-t"
          >
            <Button asChild className="flex-1">
              <a href={project.demo_url} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="mr-2 h-4 w-4" />
                Live Demo
              </a>
            </Button>
            <Button asChild variant="outline" className="flex-1">
              <a href={project.code_url} target="_blank" rel="noopener noreferrer">
                <Code className="mr-2 h-4 w-4" />
                View Code
              </a>
            </Button>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
