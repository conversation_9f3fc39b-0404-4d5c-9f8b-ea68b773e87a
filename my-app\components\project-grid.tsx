"use client"

import { motion } from "framer-motion"
import ProjectCard from "./project-card"

const projects = [
  {
    id: 1,
    title: "E-Commerce Mobile App",
    description:
      "A cross-platform mobile application built with React Native and Expo, featuring a clean UI and seamless shopping experience.",
    image: "https://via.placeholder.com/800x450.png?text=E-Commerce+App",
    tags: ["React Native", "Expo", "Redux", "Firebase"],
    demoUrl: "#",
    codeUrl: "#",
  },
  {
    id: 2,
    title: "Portfolio Website",
    description:
      "A modern, responsive portfolio website built with Next.js and Tailwind CSS, showcasing projects and skills.",
    image: "https://via.placeholder.com/800x450.png?text=Portfolio+Website",
    tags: ["Next.js", "Tailwind CSS", "Framer Motion"],
    demoUrl: "#",
    codeUrl: "#",
  },
  {
    id: 3,
    title: "Task Management Dashboard",
    description:
      "A comprehensive task management application with real-time updates, user authentication, and team collaboration features.",
    image: "https://via.placeholder.com/800x450.png?text=Task+Management",
    tags: ["React", "Node.js", "MongoDB", "Socket.io"],
    demoUrl: "#",
    codeUrl: "#",
  },
  {
    id: 4,
    title: "Weather App",
    description: "A weather application that provides real-time weather data and forecasts for locations worldwide.",
    image: "https://via.placeholder.com/800x450.png?text=Weather+App",
    tags: ["React", "OpenWeather API", "Tailwind CSS"],
    demoUrl: "#",
    codeUrl: "#",
  },
  {
    id: 5,
    title: "Blog Platform",
    description: "A full-featured blog platform with user authentication, content management, and commenting system.",
    image: "https://via.placeholder.com/800x450.png?text=Blog+Platform",
    tags: ["Next.js", "MongoDB", "NextAuth.js"],
    demoUrl: "#",
    codeUrl: "#",
  },
  {
    id: 6,
    title: "Fitness Tracker",
    description: "A mobile application for tracking workouts, nutrition, and fitness progress with data visualization.",
    image: "https://via.placeholder.com/800x450.png?text=Fitness+Tracker",
    tags: ["React Native", "Expo", "Firebase", "Chart.js"],
    demoUrl: "#",
    codeUrl: "#",
  },
]

export default function ProjectGrid() {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  }

  return (
    <motion.div
      variants={container}
      initial="hidden"
      animate="show"
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12"
    >
      {projects.map((project) => (
        <motion.div key={project.id} variants={item}>
          <ProjectCard project={project} />
        </motion.div>
      ))}
    </motion.div>
  )
}

