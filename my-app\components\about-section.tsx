"use client"

import type React from "react"
import { motion } from "framer-motion"
import {
  Code,
  Server,
  Database,
  Layout,
  Zap,
  Lightbulb,
  Smartphone,
  GitBranch,
  CheckCircle,
  GitMerge,
  Kanban,
  Brain,
  Users,
  Settings,
  TestTube,
} from "lucide-react"

// Map of icon names to components
const iconMap: Record<string, React.ReactNode> = {
  code: <Code className="w-6 h-6" />,
  server: <Server className="w-6 h-6" />,
  database: <Database className="w-6 h-6" />,
  layout: <Layout className="w-6 h-6" />,
  smartphone: <Smartphone className="w-6 h-6" />,
  zap: <Zap className="w-6 h-6" />,
  lightbulb: <Lightbulb className="w-6 h-6" />,
  "git-branch": <GitBranch className="w-6 h-6" />,
  "check-circle": <CheckCircle className="w-6 h-6" />,
  "git-merge": <GitMerge className="w-6 h-6" />,
  trello: <Kanban className="w-6 h-6" />,
  brain: <Brain className="w-6 h-6" />,
  users: <Users className="w-6 h-6" />,
  settings: <Settings className="w-6 h-6" />,
  "test-tube": <TestTube className="w-6 h-6" />,
}

// Static skills data
const skills = [
  {
    id: 1,
    name: "Frontend Development",
    tech: "React.js, MUI, Tailwind CSS, Flutter, Jetpack Compose",
    icon: "code",
    color: "text-emerald-500",
  },
  {
    id: 2,
    name: "Backend Development",
    tech: "Node.js, Express.js, Spring Boot, Pocketsflow",
    icon: "server",
    color: "text-teal-500",
  },
  {
    id: 3,
    name: "Mobile Development",
    tech: "React Native, Flutter, Jetpack Compose, Android Native",
    icon: "smartphone",
    color: "text-purple-500",
  },
  {
    id: 4,
    name: "Database & Storage",
    tech: "MongoDB, Firebase, SQLite, PostgreSQL, Offline Storage",
    icon: "database",
    color: "text-cyan-500",
  },
  {
    id: 5,
    name: "CI/CD & DevOps",
    tech: "Azure DevOps, Jenkins, Vercel, Hostinger, VPS",
    icon: "settings",
    color: "text-yellow-500",
  },
  {
    id: 6,
    name: "Testing & QA",
    tech: "Maestro E2E, Jest, JUnit, Test Automation",
    icon: "test-tube",
    color: "text-orange-500",
  },
  {
    id: 11,
    name: "OCR & Data Processing",
    tech: "OCR SDKs, OCR APIs, Data Capture, Benchmarking",
    icon: "zap",
    color: "text-red-500",
  },
  {
    id: 12,
    name: "Real-time Features",
    tech: "Firebase Push Notifications, Server Sync, Offline Mode",
    icon: "lightbulb",
    color: "text-amber-500",
  },
  {
    id: 7,
    name: "Version Control",
    tech: "Git, GitHub, Azure Repos, GitLab",
    icon: "git-merge",
    color: "text-green-500",
  },
  {
    id: 8,
    name: "Agile & Methodologies",
    tech: "Scrum, Kanban, Cross-functional Teams",
    icon: "users",
    color: "text-blue-500",
  },
  {
    id: 9,
    name: "AI & Machine Learning",
    tech: "AI Integration, SEO Optimization, Content Generation",
    icon: "brain",
    color: "text-indigo-500",
  },
  {
    id: 10,
    name: "Communication & Leadership",
    tech: "Team Collaboration, Stakeholder Management, Mentoring",
    icon: "users",
    color: "text-pink-500",
  },
]



// Static experiences data organized by field
const professionalExperiences = [
  {
    id: 1,
    period: "09/2023 - Present",
    role: "Software Engineer",
    company: "Xelops Technology",
    description:
      "Ongoing development of telecom solutions. Tech Stack: Jetpack Compose, React Native, Firebase, Azure DevOps, OCR SDK, Kotlin, Fastlane, OAuth2.0. Project 1: Sales Facilitator App - Continuously enhancing MVVM architecture, OCR SDK integration, and offline-first flows. Project 2: Collaborative Mobile App - Maintaining FCM/APN push notifications and CI/CD automation. Project 3: iOS deployment management with ongoing App Store compliance.",
  },
  {
    id: 2,
    period: "02/2023 - 08/2023",
    role: "End of Studies Intern",
    company: "Xelops Technology",
    description:
      "Project 1: Conducted comparative study of on-premise OCR SDKs, evaluating performance, offline support, and data privacy compliance. Provided detailed technical reports and recommendations. Project 2: Integrated UI from Figma designs using React Native and TypeScript, implemented REST API consumption and FCM push notifications.",
  },
]

const educationalExperiences = [
  {
    id: 5,
    period: "2018 - 2023",
    role: "Software Engineering Graduate",
    company: "University Education",
    description:
      "Bachelor's degree in Software Engineering with specialization in Mobile Development. Comprehensive curriculum covering algorithms, data structures, software architecture, and modern development practices. Strong foundation in programming languages and development methodologies.",
  },
]

const sideProjects = [
  {
    id: 4,
    period: "2025 - Present",
    role: "AI & SaaS Developer",
    company: "AI-Powered SEO Article Generator",
    description:
      "Currently developing SaaS platform to generate AI-based SEO articles for marketers and businesses. Technologies: React, MUI, Redux Toolkit Query, Supabase, Pocketsflow, VPS, Vercel, Hostinger. Impact: Enabling automated content creation, reducing article generation time and improving SEO efficiency.",
  },
  {
    id: 10,
    period: "2025 - Present",
    role: "Open Source Developer",
    company: "React Native Scalable Drawer Library",
    description:
      "Developing open source React Native library for scalable drawer components. Technologies: React Native, TypeScript, Expo. Impact: Providing developers with customizable and performant drawer solutions for mobile applications.",
  },
  {
    id: 11,
    period: "2025 - Present",
    role: "Mobile Developer",
    company: "TrackMe - Habit Tracking App",
    description:
      "Building mobile application for habit tracking and personal productivity. Technologies: Expo, React Native, TypeScript. Backend: TBD. Impact: Helping users build and maintain positive habits through intuitive tracking and analytics.",
  },
  {
    id: 8,
    period: "2024",
    role: "Mobile Developer",
    company: "Syndic Payment Management App",
    description:
      "Mobile solution for managing payments and communications related to building syndicates. Technologies: React Native. Impact: Improved accessibility and usability for residents and property managers through mobile-first features.",
  },
  {
    id: 3,
    period: "2022",
    role: "Project Leader",
    company: "Student Housing Web App (Marrakech)",
    description:
      "Led full-stack development of MERN-based web application for student apartment rentals. Technologies: MERN Stack (MongoDB, Express, React, Node.js). Impact: Streamlined housing search for students with advanced filtering and user-friendly listings. Led team coordination and project management.",
  },
  {
    id: 6,
    period: "2022",
    role: "Mobile Developer",
    company: "Event Organizer Mobile App",
    description:
      "Built app to help users find professionals (singers, chefs, etc.) for events. Technologies: Flutter, Supabase, Spring Boot. Impact: Simplified event planning by connecting users with reliable service providers.",
  },
  {
    id: 7,
    period: "2022",
    role: "Mobile Developer",
    company: "Budget Tracker Mobile App",
    description:
      "Offline-first expense tracking app for personal finance management. Technologies: React Native, SQLite. Impact: Allowed users to manage budgets on the go without internet access.",
  },
  {
    id: 9,
    period: "2022",
    role: "Full-Stack Developer",
    company: "Note-Taking Web App",
    description:
      "Built web application that allowed users to create, organize, and update personal notes with ease. Technologies: MERN Stack. Impact: Provided seamless experience for users to capture thoughts, manage tasks, and edit or delete notes anytime — improving daily productivity and organization.",
  },
]

export default function AboutSection() {
  return (
    <div className="min-h-screen p-8 md:p-12 bg-gradient-to-br from-emerald-50 to-teal-100 dark:from-gray-900 dark:to-teal-950">
      <div className="max-w-5xl mx-auto">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-4xl md:text-5xl font-bold mb-12 text-center lg:text-left"
        >
          About <span className="text-emerald-600 dark:text-emerald-400">Me</span>
        </motion.h2>

        {/* Skills & Experience */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="space-y-12 w-full"
        >
            {/* Skills */}
            <div>
              <h3 className="text-2xl font-semibold mb-6">Skills & Expertise</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {skills.map((skill, index) => (
                  <motion.div
                    key={skill.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                    className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-4 rounded-xl"
                  >
                    <div className="flex items-center mb-2">
                      <div className={skill.color}>{iconMap[skill.icon.toLowerCase()] || <Code />}</div>
                      <h4 className="ml-2 font-medium">{skill.name}</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">{skill.tech}</p>
                  </motion.div>
                ))}
              </div>
            </div>

            
            {/* Side Projects */}
            <div>
              <h3 className="text-2xl font-semibold mb-6">Side Projects</h3>
              <div className="space-y-6">
                {sideProjects.map((exp, index) => (
                  <motion.div
                    key={exp.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}
                    className="relative pl-6 border-l-2 border-purple-300 dark:border-purple-700"
                  >
                    <span className="text-sm text-muted-foreground">{exp.period}</span>
                    <h4 className="font-medium mt-1">{exp.role}</h4>
                    <p className="text-sm font-medium text-purple-600 dark:text-purple-400">{exp.company}</p>
                    <p className="text-sm mt-2 text-muted-foreground">{exp.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Professional Experience */}
            <div>
              <h3 className="text-2xl font-semibold mb-6">Professional Experience</h3>
              <div className="space-y-6">
                {professionalExperiences.map((exp, index) => (
                  <motion.div
                    key={exp.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
                    className="relative pl-6 border-l-2 border-emerald-300 dark:border-emerald-700"
                  >
                    <span className="text-sm text-muted-foreground">{exp.period}</span>
                    <h4 className="font-medium mt-1">{exp.role}</h4>
                    <p className="text-sm font-medium text-emerald-600 dark:text-emerald-400">{exp.company}</p>
                    <p className="text-sm mt-2 text-muted-foreground">{exp.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Education */}
            <div>
              <h3 className="text-2xl font-semibold mb-6">Education</h3>
              <div className="space-y-6">
                {educationalExperiences.map((exp, index) => (
                  <motion.div
                    key={exp.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}
                    className="relative pl-6 border-l-2 border-blue-300 dark:border-blue-700"
                  >
                    <span className="text-sm text-muted-foreground">{exp.period}</span>
                    <h4 className="font-medium mt-1">{exp.role}</h4>
                    <p className="text-sm font-medium text-blue-600 dark:text-blue-400">{exp.company}</p>
                    <p className="text-sm mt-2 text-muted-foreground">{exp.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>
        </motion.div>
      </div>
    </div>
  )
}

