"use client"

import { motion } from "framer-motion"
import { SectionHeader } from "./section-header"

const technologies = [
  { name: "React", icon: "devicon-react-original" },
  { name: "Next.js", icon: "devicon-nextjs-original" },
  { name: "Expo", icon: "devicon-react-original" }, // Using React icon as placeholder for Expo
  { name: "TypeScript", icon: "devicon-typescript-plain" },
  { name: "Jetpack Compose", icon: "devicon-android-plain" }, // Using Android icon as placeholder
  { name: "Azure DevOps", icon: "devicon-azure-plain" },
  { name: "Node.js", icon: "devicon-nodejs-plain" },
  { name: "Tailwind CSS", icon: "devicon-tailwindcss-plain" },
]

export default function TechStackSection() {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  }

  return (
    <section className="py-20 bg-muted">
      <div className="container mx-auto px-4">
        <SectionHeader title="Tech Stack" description="The technologies I use to bring ideas to life" />

        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-8 mt-12"
        >
          {technologies.map((tech, index) => (
            <motion.div
              key={index}
              variants={item}
              className="flex flex-col items-center p-6 bg-background rounded-lg shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="w-16 h-16 flex items-center justify-center mb-4 text-4xl text-primary">
                <i className={tech.icon}></i>
              </div>
              <h3 className="text-lg font-medium">{tech.name}</h3>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

