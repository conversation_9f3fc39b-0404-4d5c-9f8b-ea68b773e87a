"use client"

import { motion } from "framer-motion"
import { Code, Database, Server, Zap, Smartphone, Cloud } from "lucide-react"
import Image from "next/image"

export default function About() {
  const skills = [
    {
      icon: <Smartphone className="w-8 h-8 text-orange-500" />,
      title: "Mobile",
      description: "React Native, Jetpack Compose, Kotlin",
    },
    {
      icon: <Code className="w-8 h-8 text-blue-500" />,
      title: "Frontend",
      description: "React, Next.js, Redux, Tailwind CSS",
    },
    {
      icon: <Server className="w-8 h-8 text-green-500" />,
      title: "Backend",
      description: "Node.js, Express, REST APIs",
    },
    {
      icon: <Database className="w-8 h-8 text-purple-500" />,
      title: "Database",
      description: "MongoDB, Firebase",
    },
    {
      icon: <Cloud className="w-8 h-8 text-cyan-500" />,
      title: "DevOps",
      description: "Azure DevOps, CI/CD, Fastlane",
    },
    {
      icon: <Zap className="w-8 h-8 text-yellow-500" />,
      title: "Testing",
      description: "E2E Testing, Unit Testing, Maestro",
    },
  ]

  return (
    <section
      id="about"
      className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-900 transition-colors duration-300 overflow-hidden relative"
    >
      <div className="container mx-auto px-6 relative z-10">
        <motion.h2
          className="text-4xl font-bold mb-8 text-center dark:text-white"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          À Propos
        </motion.h2>
        <div className="flex flex-col md:flex-row items-center justify-between">
          <motion.div
            className="md:w-1/2 mb-8 md:mb-0"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <p className="text-xl text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
              Développeur Mobile et MERN Stack passionné, je me spécialise dans la création dapplications robustes et
              conviviales. Avec une solide expérience en React Native, Jetpack Compose et technologies web, je crée des
              expériences numériques fluides sur différentes plateformes.
            </p>
            <p className="text-xl text-gray-700 dark:text-gray-300 leading-relaxed">
              Mon expertise sétend aux pipelines CI/CD, aux stratégies de test et aux pratiques DevOps avec Azure
              DevOps et Fastlane. Diplômé en Génie Informatique de lENSA Marrakech, je mengage à écrire du code propre
              et efficace. Je partage également mes connaissances à travers des ateliers techniques et des articles sur{" "}
              <a
                href="https://medium.com/@yessinejawa"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:underline"
              >
                Medium
              </a>
              .
            </p>
          </motion.div>
          <motion.div
            className="md:w-1/2 grid grid-cols-2 gap-6"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            {skills.map((skill, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                {skill.icon}
                <h3 className="text-xl font-semibold mt-4 mb-2 dark:text-white">{skill.title}</h3>
                <p className="text-gray-600 dark:text-gray-300">{skill.description}</p>
              </div>
            ))}
          </motion.div>
        </div>
      </div>
      <div className="absolute bottom-0 right-0 w-64 h-64 -mb-32 -mr-32 opacity-20">
        <Image src="/images/decorative-bg.svg" alt="Decorative background" width={256} height={256} />
      </div>
    </section>
  )
}

