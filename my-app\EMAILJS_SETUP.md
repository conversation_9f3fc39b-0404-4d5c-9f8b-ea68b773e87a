# EmailJS Setup Guide

This guide will help you set up EmailJS to handle contact form submissions in your portfolio.

## Step 1: Create EmailJS Account

1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

## Step 2: Create Email Service

1. In your EmailJS dashboard, go to **Email Services**
2. Click **Add New Service**
3. Choose your email provider (Gmail, Outlook, etc.)
4. Follow the setup instructions for your provider
5. Note down your **Service ID**

## Step 3: Create Email Template

1. Go to **Email Templates** in your dashboard
2. Click **Create New Template**
3. Use this template structure:

```
Subject: New Contact Form Submission

From: {{from_email}}

Message:
{{message}}

---
This message was sent from your portfolio contact form.
```

4. Save the template and note down your **Template ID**

## Step 4: Get Public Key

1. Go to **Account** > **General**
2. Find your **Public Key** (also called User ID)
3. Copy this key

## Step 5: Configure Environment Variables

1. Copy `.env.local.example` to `.env.local`:
   ```bash
   cp .env.local.example .env.local
   ```

2. Edit `.env.local` and add your EmailJS credentials:
   ```
   NEXT_PUBLIC_EMAILJS_SERVICE_ID=your_actual_service_id
   NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=your_actual_template_id
   NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=your_actual_public_key
   ```

## Step 6: Update Your Email

Update the `profile.email` in `components/contact-section.tsx` to your actual email address where you want to receive messages.

## Step 7: Test the Form

1. Start your development server: `npm run dev`
2. Navigate to the contact section
3. Fill out the form with a test email and message
4. Submit the form
5. Check your email inbox for the message

## Troubleshooting

- **Form not sending**: Check browser console for errors
- **Environment variables not working**: Restart your development server after adding `.env.local`
- **Emails not received**: Check your spam folder and verify EmailJS template configuration
- **CORS errors**: Make sure you're using the correct domain in EmailJS settings

## Free Tier Limits

EmailJS free tier includes:
- 200 emails per month
- 2 email services
- 1 email template

This should be sufficient for most portfolio contact forms.
