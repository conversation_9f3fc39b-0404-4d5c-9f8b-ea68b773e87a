{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/social-links.tsx"], "sourcesContent": ["\"use client\"\n\nimport type React from \"react\"\nimport { Gith<PERSON>, Linkedin, Twitter, Instagram, Facebook, Youtube, Dribbble, Figma } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface SocialLinksProps {\n  className?: string\n  iconClassName?: string\n}\n\n// Static social links data\nconst socialLinks = [\n  {\n    id: 1,\n    name: \"GitHub\",\n    url: \"https://github.com/apachi1444\",\n    icon: \"github\",\n  },\n  {\n    id: 2,\n    name: \"LinkedIn\",\n    url: \"https://linkedin.com/in/yessine-jaoua\",\n    icon: \"linkedin\",\n  },\n  {\n    id: 3,\n    name: \"Twitter\",\n    url: \"https://twitter.com/JawaYessine\",\n    icon: \"twitter\",\n  },\n]\n\n// Map of icon names to components\nconst iconMap: Record<string, React.ReactNode> = {\n  github: <Github />,\n  linkedin: <Linkedin />,\n  twitter: <Twitter />,\n  instagram: <Instagram />,\n  facebook: <Facebook />,\n  youtube: <Youtube />,\n  dribbble: <Dribbble />,\n  figma: <Figma />,\n}\n\nexport default function SocialLinks({ className, iconClassName }: SocialLinksProps) {\n  return (\n    <div className={cn(\"flex space-x-4\", className)}>\n      {socialLinks.map((link) => (\n        <a\n          key={link.id}\n          href={link.url}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors duration-300\"\n          aria-label={link.name}\n        >\n          <div className={cn(\"h-5 w-5\", iconClassName)}>{iconMap[link.icon.toLowerCase()] || <Github />}</div>\n        </a>\n      ))}\n    </div>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAIA;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAWA,2BAA2B;AAC3B,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,MAAM;IACR;CACD;AAED,kCAAkC;AAClC,MAAM,UAA2C;IAC/C,sBAAQ,6LAAC,yMAAA,CAAA,SAAM;;;;;IACf,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;;;;;IACnB,uBAAS,6LAAC,2MAAA,CAAA,UAAO;;;;;IACjB,yBAAW,6LAAC,+MAAA,CAAA,YAAS;;;;;IACrB,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;;;;;IACnB,uBAAS,6LAAC,2MAAA,CAAA,UAAO;;;;;IACjB,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;;;;;IACnB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;;;;;AACf;AAEe,SAAS,YAAY,EAAE,SAAS,EAAE,aAAa,EAAoB;IAChF,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;gBAEC,MAAM,KAAK,GAAG;gBACd,QAAO;gBACP,KAAI;gBACJ,WAAU;gBACV,cAAY,KAAK,IAAI;0BAErB,cAAA,6LAAC;oBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,WAAW;8BAAiB,OAAO,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,kBAAI,6LAAC,yMAAA,CAAA,SAAM;;;;;;;;;;eAPrF,KAAK,EAAE;;;;;;;;;;AAYtB;KAjBwB"}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { motion } from \"framer-motion\"\nimport { Menu, X, Home, User, Briefcase, Award, BookOpen, Mail, BarChart3 } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"./ui/button\"\nimport { cn } from \"@/lib/utils\"\nimport type { SectionType } from \"./split-layout\"\nimport Image from \"next/image\"\nimport SocialLinks from \"./social-links\"\n\n// Static profile data\nconst profile = {\n  name: \"Yessine JAOUA\",\n  title: \"Web & Mobile Developer\",\n  avatar_url: \"/assets/profile-image.jpg\",\n}\n\ninterface SidebarProps {\n  activeSection: SectionType\n  onSectionChange: (section: SectionType) => void\n  showAdmin?: boolean\n}\n\nexport default function Sidebar({ activeSection, onSectionChange, showAdmin = false }: SidebarProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  const navItems = [\n    { id: \"home\", label: \"Home\", icon: <Home className=\"w-5 h-5\" /> },\n    { id: \"about\", label: \"About\", icon: <User className=\"w-5 h-5\" /> },\n    { id: \"portfolio\", label: \"Portfolio\", icon: <Briefcase className=\"w-5 h-5\" /> },\n    { id: \"awards\", label: \"Awards\", icon: <Award className=\"w-5 h-5\" /> },\n    { id: \"blog\", label: \"Blog\", icon: <BookOpen className=\"w-5 h-5\" /> },\n    { id: \"contact\", label: \"Contact\", icon: <Mail className=\"w-5 h-5\" /> },\n  ]\n\n  // Add admin section if enabled\n  if (showAdmin) {\n    navItems.push({ id: \"admin\", label: \"Admin\", icon: <BarChart3 className=\"w-5 h-5\" /> })\n  }\n\n  // Get background color based on active section\n  const getBackgroundColor = () => {\n    if (!mounted) return \"\"\n\n    switch (activeSection) {\n      case \"home\":\n        return \"bg-gradient-to-br from-violet-500 to-indigo-600 dark:from-violet-900 dark:to-indigo-950\"\n      case \"about\":\n        return \"bg-gradient-to-br from-emerald-500 to-teal-600 dark:from-emerald-900 dark:to-teal-950\"\n      case \"portfolio\":\n        return \"bg-gradient-to-br from-amber-500 to-orange-600 dark:from-amber-900 dark:to-orange-950\"\n      case \"awards\":\n        return \"bg-gradient-to-br from-yellow-500 to-amber-600 dark:from-yellow-900 dark:to-amber-950\"\n      case \"blog\":\n        return \"bg-gradient-to-br from-rose-500 to-pink-600 dark:from-rose-900 dark:to-pink-950\"\n      case \"contact\":\n        return \"bg-gradient-to-br from-blue-500 to-cyan-600 dark:from-blue-900 dark:to-cyan-950\"\n      case \"admin\":\n        return \"bg-gradient-to-br from-gray-500 to-slate-600 dark:from-gray-900 dark:to-slate-950\"\n      default:\n        return \"bg-gradient-to-br from-violet-500 to-indigo-600 dark:from-violet-900 dark:to-indigo-950\"\n    }\n  }\n\n  return (\n    <>\n      {/* Mobile Header */}\n      <header className=\"lg:hidden fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-md border-b border-border shadow-sm\">\n        <div className=\"flex items-center justify-between p-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 rounded-full overflow-hidden\">\n              <Image\n                src={profile.avatar_url || \"/placeholder.svg\"}\n                alt={profile.name}\n                width={40}\n                height={40}\n                className=\"object-cover\"\n              />\n            </div>\n            <span className=\"font-bold text-lg\">{profile.name}</span>\n          </div>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            aria-label=\"Toggle menu\"\n            className=\"hover:bg-muted/80 focus:bg-muted/80 transition-colors duration-200\"\n          >\n            {isMobileMenuOpen ? (\n              <X className=\"h-6 w-6 text-foreground\" />\n            ) : (\n              <Menu className=\"h-6 w-6 text-foreground\" />\n            )}\n          </Button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"border-t border-border bg-background/95 backdrop-blur-md\"\n          >\n            <nav className=\"p-4 flex flex-col space-y-2\">\n              {navItems.map((item) => (\n                <Button\n                  key={item.id}\n                  variant=\"ghost\"\n                  className={cn(\n                    \"justify-start h-12 text-base font-medium transition-colors duration-200\",\n                    activeSection === item.id\n                      ? \"bg-primary/10 text-primary border border-primary/20\"\n                      : \"hover:bg-muted/80\"\n                  )}\n                  onClick={() => {\n                    onSectionChange(item.id as SectionType)\n                    setIsMobileMenuOpen(false)\n                  }}\n                >\n                  <div className=\"flex items-center\">\n                    {item.icon}\n                    <span className=\"ml-3\">{item.label}</span>\n                  </div>\n                </Button>\n              ))}\n            </nav>\n          </motion.div>\n        )}\n      </header>\n\n      {/* Desktop Sidebar */}\n      <aside\n        className={cn(\n          \"fixed top-0 left-0 bottom-0 w-[35vw] hidden lg:flex flex-col transition-colors duration-500 ease-in-out z-30\",\n          getBackgroundColor(),\n        )}\n      >\n        <div className=\"flex flex-col justify-between h-full p-12 text-white\">\n          {/* Profile Section */}\n          <div className=\"space-y-8\">\n            <div className=\"relative w-32 h-32 mx-auto\">\n              <div className=\"absolute inset-0 rounded-full bg-white/20 blur-md\"></div>\n              <div className=\"absolute inset-1 rounded-full overflow-hidden\">\n                <Image\n                  src={profile.avatar_url || \"/placeholder.svg\"}\n                  alt={profile.name}\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <h1 className=\"text-3xl font-bold\">{profile.name}</h1>\n              <p className=\"text-white/80 mt-2\">{profile.title}</p>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"space-y-6\">\n            {navItems.map((item) => (\n              <button\n                key={item.id}\n                onClick={() => onSectionChange(item.id as SectionType)}\n                className={cn(\n                  \"flex items-center w-full px-6 py-3 rounded-lg transition-all duration-300\",\n                  activeSection === item.id\n                    ? \"bg-white/20 backdrop-blur-sm shadow-lg transform translate-x-2\"\n                    : \"hover:bg-white/10 hover:translate-x-1\",\n                )}\n              >\n                <div className=\"flex items-center\">\n                  {item.icon}\n                  <span className=\"ml-3 text-lg font-medium\">{item.label}</span>\n                </div>\n                {activeSection === item.id && (\n                  <motion.div\n                    layoutId=\"activeIndicator\"\n                    className=\"ml-auto w-2 h-2 rounded-full bg-white\"\n                    transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n                  />\n                )}\n              </button>\n            ))}\n          </nav>\n\n          {/* Social Links */}\n          <div className=\"text-center\">\n            <SocialLinks className=\"justify-center\" />\n            <p className=\"mt-4 text-sm text-white/70\">\n              © {new Date().getFullYear()} {profile.name}\n            </p>\n          </div>\n        </div>\n      </aside>\n\n      {/* Padding for mobile view to account for fixed header */}\n      <div className=\"h-16 lg:hidden\"></div>\n    </>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AACA;AAEA;AACA;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADA;;;AAHA;;;;;;;;AAWA,sBAAsB;AACtB,MAAM,UAAU;IACd,MAAM;IACN,OAAO;IACP,YAAY;AACd;AAQe,SAAS,QAAQ,EAAE,aAAa,EAAE,eAAe,EAAE,YAAY,KAAK,EAAgB;;IACjG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,WAAW;QACb;4BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,IAAI;YAAQ,OAAO;YAAQ,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QAAa;QAChE;YAAE,IAAI;YAAS,OAAO;YAAS,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QAAa;QAClE;YAAE,IAAI;YAAa,OAAO;YAAa,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAAa;QAC/E;YAAE,IAAI;YAAU,OAAO;YAAU,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAAa;QACrE;YAAE,IAAI;YAAQ,OAAO;YAAQ,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAAa;QACpE;YAAE,IAAI;YAAW,OAAO;YAAW,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QAAa;KACvE;IAED,+BAA+B;IAC/B,IAAI,WAAW;QACb,SAAS,IAAI,CAAC;YAAE,IAAI;YAAS,OAAO;YAAS,oBAAM,6LAAC,qNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAAa;IACvF;IAEA,+CAA+C;IAC/C,MAAM,qBAAqB;QACzB,IAAI,CAAC,SAAS,OAAO;QAErB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE;;0BAEE,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,QAAQ,UAAU,IAAI;4CAC3B,KAAK,QAAQ,IAAI;4CACjB,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAGd,6LAAC;wCAAK,WAAU;kDAAqB,QAAQ,IAAI;;;;;;;;;;;;0CAEnD,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,oBAAoB,CAAC;gCACpC,cAAW;gCACX,WAAU;0CAET,iCACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMrB,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,8HAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2EACA,kBAAkB,KAAK,EAAE,GACrB,wDACA;oCAEN,SAAS;wCACP,gBAAgB,KAAK,EAAE;wCACvB,oBAAoB;oCACtB;8CAEA,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI;0DACV,6LAAC;gDAAK,WAAU;0DAAQ,KAAK,KAAK;;;;;;;;;;;;mCAf/B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAyBxB,6LAAC;gBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gHACA;0BAGF,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,UAAU,IAAI;gDAC3B,KAAK,QAAQ,IAAI;gDACjB,IAAI;gDACJ,WAAU;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsB,QAAQ,IAAI;;;;;;sDAChD,6LAAC;4CAAE,WAAU;sDAAsB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;sCAKpD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;oCAEC,SAAS,IAAM,gBAAgB,KAAK,EAAE;oCACtC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6EACA,kBAAkB,KAAK,EAAE,GACrB,mEACA;;sDAGN,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI;8DACV,6LAAC;oDAAK,WAAU;8DAA4B,KAAK,KAAK;;;;;;;;;;;;wCAEvD,kBAAkB,KAAK,EAAE,kBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,UAAS;4CACT,WAAU;4CACV,YAAY;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;;;;;;;mCAjBzD,KAAK,EAAE;;;;;;;;;;sCAyBlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iIAAA,CAAA,UAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAE,WAAU;;wCAA6B;wCACrC,IAAI,OAAO,WAAW;wCAAG;wCAAE,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0BAOlD,6LAAC;gBAAI,WAAU;;;;;;;;AAGrB;GAvLwB;KAAA"}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/home-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { ArrowRight } from \"lucide-react\"\nimport { Button } from \"@/components/ui/button\"\n\n// Static profile data\nconst profile = {\n  name: \"Yessine JAOUA\",\n  title: \"Web & Mobile Developer\",\n  bio : \"Passionate Web & Mobile Developer specializing in cross-platform apps with React Native and Jetpack Compose. Experienced in building SaaS web apps using React, Next.js, and a modern backend stack: Supabase, NestJS, Drizzle ORM. Skilled with Redux Toolkit Query, React Query, and React Hook Form for state and form management.\"\n}\n\nexport default function HomeSection() {\n\n  // Remove the unused variable\n  // const isDark = mounted && resolvedTheme === \"dark\"\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-violet-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-950\">\n      <div className=\"max-w-3xl\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"text-center lg:text-left\"\n        >\n          <motion.div\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            transition={{ delay: 0.2, duration: 0.5 }}\n            className=\"mb-6 inline-block\"\n          >\n            <div className=\"text-sm font-medium px-4 py-2 rounded-full bg-violet-200 dark:bg-violet-900/50 text-violet-800 dark:text-violet-200\">\n              {profile.title}\n            </div>\n          </motion.div>\n\n          <motion.h1\n            className=\"text-5xl md:text-7xl font-bold mb-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.5 }}\n          >\n            Creating digital{\" \"}\n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-violet-600 to-indigo-600 dark:from-violet-400 dark:to-indigo-400\">\n              experiences\n            </span>{\" \"}\n            that matter\n          </motion.h1>\n\n          <motion.p\n            className=\"text-xl text-muted-foreground mb-8\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.5 }}\n          >\n            {profile.bio}\n          </motion.p>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.5, duration: 0.5 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\"\n          >\n            <Button\n              size=\"lg\"\n              className=\"rounded-full group\"\n              onClick={() => {\n                // Update URL and trigger section change\n                window.history.pushState({}, \"\", \"#portfolio\")\n\n                // For mobile view, scroll to portfolio section\n                if (window.innerWidth < 1024) {\n                  const portfolioSection = document.getElementById(\"portfolio\")\n                  if (portfolioSection) {\n                    portfolioSection.scrollIntoView({ behavior: \"smooth\" })\n                  }\n                } else {\n                  // For desktop view, trigger a custom event to change section\n                  window.dispatchEvent(new CustomEvent('navigate-to-section', {\n                    detail: { section: 'portfolio' }\n                  }))\n                }\n              }}\n            >\n              View My Work\n              <ArrowRight className=\"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\" />\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              size=\"lg\"\n              className=\"rounded-full\"\n              onClick={() => {\n                const contactSection = document.getElementById(\"contact\")\n                if (contactSection) {\n                  contactSection.scrollIntoView({ behavior: \"smooth\" })\n                }\n              }}\n            >\n              Get in Touch\n            </Button>\n          </motion.div>\n        </motion.div>\n\n        {/* Animated background elements */}\n        <div className=\"absolute inset-0 -z-10 overflow-hidden\">\n          <motion.div\n            className=\"absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-violet-400/20 dark:bg-violet-700/20 blur-3xl\"\n            animate={{\n              x: [0, 30, 0],\n              y: [0, -30, 0],\n            }}\n            transition={{\n              repeat: Number.POSITIVE_INFINITY,\n              duration: 8,\n              ease: \"easeInOut\",\n            }}\n          />\n          <motion.div\n            className=\"absolute bottom-1/4 left-1/4 w-64 h-64 rounded-full bg-indigo-400/20 dark:bg-indigo-700/20 blur-3xl\"\n            animate={{\n              x: [0, -30, 0],\n              y: [0, 30, 0],\n            }}\n            transition={{\n              repeat: Number.POSITIVE_INFINITY,\n              duration: 10,\n              ease: \"easeInOut\",\n            }}\n          />\n        </div>\n      </div>\n    </div>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAIA;AAFA;AACA;AAHA;;;;;AAMA,sBAAsB;AACtB,MAAM,UAAU;IACd,MAAM;IACN,OAAO;IACP,KAAM;AACR;AAEe,SAAS;IAEtB,6BAA6B;IAC7B,qDAAqD;IAErD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,KAAK;;;;;;;;;;;sCAIlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;;gCACzC;gCACkB;8CACjB,6LAAC;oCAAK,WAAU;8CAAuH;;;;;;gCAE/H;gCAAI;;;;;;;sCAId,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;sCAEvC,QAAQ,GAAG;;;;;;sCAGd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,8HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,wCAAwC;wCACxC,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI;wCAEjC,+CAA+C;wCAC/C,IAAI,OAAO,UAAU,GAAG,MAAM;4CAC5B,MAAM,mBAAmB,SAAS,cAAc,CAAC;4CACjD,IAAI,kBAAkB;gDACpB,iBAAiB,cAAc,CAAC;oDAAE,UAAU;gDAAS;4CACvD;wCACF,OAAO;4CACL,6DAA6D;4CAC7D,OAAO,aAAa,CAAC,IAAI,YAAY,uBAAuB;gDAC1D,QAAQ;oDAAE,SAAS;gDAAY;4CACjC;wCACF;oCACF;;wCACD;sDAEC,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAGxB,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;wCAC/C,IAAI,gBAAgB;4CAClB,eAAe,cAAc,CAAC;gDAAE,UAAU;4CAAS;wCACrD;oCACF;8CACD;;;;;;;;;;;;;;;;;;8BAOL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;gCACb,GAAG;oCAAC;oCAAG,CAAC;oCAAI;iCAAE;4BAChB;4BACA,YAAY;gCACV,QAAQ,OAAO,iBAAiB;gCAChC,UAAU;gCACV,MAAM;4BACR;;;;;;sCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,GAAG;oCAAC;oCAAG,CAAC;oCAAI;iCAAE;gCACd,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;4BACf;4BACA,YAAY;gCACV,QAAQ,OAAO,iBAAiB;gCAChC,UAAU;gCACV,MAAM;4BACR;;;;;;;;;;;;;;;;;;;;;;;AAMZ;KA5HwB"}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/about-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport type React from \"react\"\nimport { motion } from \"framer-motion\"\nimport {\n  Code,\n  Server,\n  Database,\n  Layout,\n  Zap,\n  Lightbulb,\n  Smartphone,\n  GitBranch,\n  CheckCircle,\n  GitMerge,\n  Kanban,\n  Brain,\n  Users,\n  Settings,\n  TestTube,\n} from \"lucide-react\"\n\n// Map of icon names to components\nconst iconMap: Record<string, React.ReactNode> = {\n  code: <Code className=\"w-6 h-6\" />,\n  server: <Server className=\"w-6 h-6\" />,\n  database: <Database className=\"w-6 h-6\" />,\n  layout: <Layout className=\"w-6 h-6\" />,\n  smartphone: <Smartphone className=\"w-6 h-6\" />,\n  zap: <Zap className=\"w-6 h-6\" />,\n  lightbulb: <Lightbulb className=\"w-6 h-6\" />,\n  \"git-branch\": <GitBranch className=\"w-6 h-6\" />,\n  \"check-circle\": <CheckCircle className=\"w-6 h-6\" />,\n  \"git-merge\": <GitMerge className=\"w-6 h-6\" />,\n  trello: <Kanban className=\"w-6 h-6\" />,\n  brain: <Brain className=\"w-6 h-6\" />,\n  users: <Users className=\"w-6 h-6\" />,\n  settings: <Settings className=\"w-6 h-6\" />,\n  \"test-tube\": <TestTube className=\"w-6 h-6\" />,\n}\n\n// Static skills data\nconst skills = [\n  {\n    id: 1,\n    name: \"Frontend Development\",\n    tech: \"React.js, MUI, Tailwind CSS, Flutter, Jetpack Compose\",\n    icon: \"code\",\n    color: \"text-emerald-500\",\n  },\n  {\n    id: 2,\n    name: \"Backend Development\",\n    tech: \"Node.js, Express.js, Spring Boot, Pocketsflow\",\n    icon: \"server\",\n    color: \"text-teal-500\",\n  },\n  {\n    id: 3,\n    name: \"Mobile Development\",\n    tech: \"React Native, Flutter, Jetpack Compose, Android Native\",\n    icon: \"smartphone\",\n    color: \"text-purple-500\",\n  },\n  {\n    id: 4,\n    name: \"Database & Storage\",\n    tech: \"MongoDB, Firebase, SQLite, PostgreSQL, Offline Storage\",\n    icon: \"database\",\n    color: \"text-cyan-500\",\n  },\n  {\n    id: 5,\n    name: \"CI/CD & DevOps\",\n    tech: \"Azure DevOps, Jenkins, Vercel, Hostinger, VPS\",\n    icon: \"settings\",\n    color: \"text-yellow-500\",\n  },\n  {\n    id: 6,\n    name: \"Testing & QA\",\n    tech: \"Maestro E2E, Jest, JUnit, Test Automation\",\n    icon: \"test-tube\",\n    color: \"text-orange-500\",\n  },\n  {\n    id: 11,\n    name: \"OCR & Data Processing\",\n    tech: \"OCR SDKs, OCR APIs, Data Capture, Benchmarking\",\n    icon: \"zap\",\n    color: \"text-red-500\",\n  },\n  {\n    id: 12,\n    name: \"Real-time Features\",\n    tech: \"Firebase Push Notifications, Server Sync, Offline Mode\",\n    icon: \"lightbulb\",\n    color: \"text-amber-500\",\n  },\n  {\n    id: 7,\n    name: \"Version Control\",\n    tech: \"Git, GitHub, Azure Repos, GitLab\",\n    icon: \"git-merge\",\n    color: \"text-green-500\",\n  },\n  {\n    id: 8,\n    name: \"Agile & Methodologies\",\n    tech: \"Scrum, Kanban, Cross-functional Teams\",\n    icon: \"users\",\n    color: \"text-blue-500\",\n  },\n  {\n    id: 9,\n    name: \"AI & Machine Learning\",\n    tech: \"AI Integration, SEO Optimization, Content Generation\",\n    icon: \"brain\",\n    color: \"text-indigo-500\",\n  },\n  {\n    id: 10,\n    name: \"Communication & Leadership\",\n    tech: \"Team Collaboration, Stakeholder Management, Mentoring\",\n    icon: \"users\",\n    color: \"text-pink-500\",\n  },\n]\n\n\n\n// Static experiences data organized by field\nconst professionalExperiences = [\n  {\n    id: 1,\n    period: \"09/2023 - Present\",\n    role: \"Software Engineer\",\n    company: \"Xelops Technology\",\n    description:\n      \"Ongoing development of telecom solutions. Tech Stack: Jetpack Compose, React Native, Firebase, Azure DevOps, OCR SDK, Kotlin, Fastlane, OAuth2.0. Project 1: Sales Facilitator App - Continuously enhancing MVVM architecture, OCR SDK integration, and offline-first flows. Project 2: Collaborative Mobile App - Maintaining FCM/APN push notifications and CI/CD automation. Project 3: iOS deployment management with ongoing App Store compliance.\",\n  },\n  {\n    id: 2,\n    period: \"02/2023 - 08/2023\",\n    role: \"End of Studies Intern\",\n    company: \"Xelops Technology\",\n    description:\n      \"Project 1: Conducted comparative study of on-premise OCR SDKs, evaluating performance, offline support, and data privacy compliance. Provided detailed technical reports and recommendations. Project 2: Integrated UI from Figma designs using React Native and TypeScript, implemented REST API consumption and FCM push notifications.\",\n  },\n]\n\nconst educationalExperiences = [\n  {\n    id: 5,\n    period: \"2018 - 2023\",\n    role: \"Software Engineering Graduate\",\n    company: \"University Education\",\n    description:\n      \"Bachelor's degree in Software Engineering with specialization in Mobile Development. Comprehensive curriculum covering algorithms, data structures, software architecture, and modern development practices. Strong foundation in programming languages and development methodologies.\",\n  },\n]\n\nconst sideProjects = [\n  {\n    id: 4,\n    period: \"2025 - Present\",\n    role: \"AI & SaaS Developer\",\n    company: \"AI-Powered SEO Article Generator\",\n    description:\n      \"Currently developing SaaS platform to generate AI-based SEO articles for marketers and businesses. Technologies: React, MUI, Redux Toolkit Query, Supabase, Pocketsflow, VPS, Vercel, Hostinger. Impact: Enabling automated content creation, reducing article generation time and improving SEO efficiency.\",\n  },\n  {\n    id: 10,\n    period: \"2025 - Present\",\n    role: \"Open Source Developer\",\n    company: \"React Native Scalable Drawer Library\",\n    description:\n      \"Developing open source React Native library for scalable drawer components. Technologies: React Native, TypeScript, Expo. Impact: Providing developers with customizable and performant drawer solutions for mobile applications.\",\n  },\n  {\n    id: 11,\n    period: \"2025 - Present\",\n    role: \"Mobile Developer\",\n    company: \"TrackMe - Habit Tracking App\",\n    description:\n      \"Building mobile application for habit tracking and personal productivity. Technologies: Expo, React Native, TypeScript. Backend: TBD. Impact: Helping users build and maintain positive habits through intuitive tracking and analytics.\",\n  },\n  {\n    id: 8,\n    period: \"2024\",\n    role: \"Mobile Developer\",\n    company: \"Syndic Payment Management App\",\n    description:\n      \"Mobile solution for managing payments and communications related to building syndicates. Technologies: React Native. Impact: Improved accessibility and usability for residents and property managers through mobile-first features.\",\n  },\n  {\n    id: 3,\n    period: \"2022\",\n    role: \"Project Leader\",\n    company: \"Student Housing Web App (Marrakech)\",\n    description:\n      \"Led full-stack development of MERN-based web application for student apartment rentals. Technologies: MERN Stack (MongoDB, Express, React, Node.js). Impact: Streamlined housing search for students with advanced filtering and user-friendly listings. Led team coordination and project management.\",\n  },\n  {\n    id: 6,\n    period: \"2022\",\n    role: \"Mobile Developer\",\n    company: \"Event Organizer Mobile App\",\n    description:\n      \"Built app to help users find professionals (singers, chefs, etc.) for events. Technologies: Flutter, Supabase, Spring Boot. Impact: Simplified event planning by connecting users with reliable service providers.\",\n  },\n  {\n    id: 7,\n    period: \"2022\",\n    role: \"Mobile Developer\",\n    company: \"Budget Tracker Mobile App\",\n    description:\n      \"Offline-first expense tracking app for personal finance management. Technologies: React Native, SQLite. Impact: Allowed users to manage budgets on the go without internet access.\",\n  },\n  {\n    id: 9,\n    period: \"2022\",\n    role: \"Full-Stack Developer\",\n    company: \"Note-Taking Web App\",\n    description:\n      \"Built web application that allowed users to create, organize, and update personal notes with ease. Technologies: MERN Stack. Impact: Provided seamless experience for users to capture thoughts, manage tasks, and edit or delete notes anytime — improving daily productivity and organization.\",\n  },\n]\n\nexport default function AboutSection() {\n  return (\n    <div className=\"min-h-screen p-8 md:p-12 bg-gradient-to-br from-emerald-50 to-teal-100 dark:from-gray-900 dark:to-teal-950\">\n      <div className=\"max-w-5xl mx-auto\">\n        <motion.h2\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"text-4xl md:text-5xl font-bold mb-12 text-center lg:text-left\"\n        >\n          About <span className=\"text-emerald-600 dark:text-emerald-400\">Me</span>\n        </motion.h2>\n\n        {/* Skills & Experience */}\n        <motion.div\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5, delay: 0.3 }}\n          className=\"space-y-12 w-full\"\n        >\n            {/* Skills */}\n            <div>\n              <h3 className=\"text-2xl font-semibold mb-6\">Skills & Expertise</h3>\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n                {skills.map((skill, index) => (\n                  <motion.div\n                    key={skill.id}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}\n                    className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-4 rounded-xl\"\n                  >\n                    <div className=\"flex items-center mb-2\">\n                      <div className={skill.color}>{iconMap[skill.icon.toLowerCase()] || <Code />}</div>\n                      <h4 className=\"ml-2 font-medium\">{skill.name}</h4>\n                    </div>\n                    <p className=\"text-sm text-muted-foreground\">{skill.tech}</p>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n\n            \n            {/* Side Projects */}\n            <div>\n              <h3 className=\"text-2xl font-semibold mb-6\">Side Projects</h3>\n              <div className=\"space-y-6\">\n                {sideProjects.map((exp, index) => (\n                  <motion.div\n                    key={exp.id}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}\n                    className=\"relative pl-6 border-l-2 border-purple-300 dark:border-purple-700\"\n                  >\n                    <span className=\"text-sm text-muted-foreground\">{exp.period}</span>\n                    <h4 className=\"font-medium mt-1\">{exp.role}</h4>\n                    <p className=\"text-sm font-medium text-purple-600 dark:text-purple-400\">{exp.company}</p>\n                    <p className=\"text-sm mt-2 text-muted-foreground\">{exp.description}</p>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n\n            {/* Professional Experience */}\n            <div>\n              <h3 className=\"text-2xl font-semibold mb-6\">Professional Experience</h3>\n              <div className=\"space-y-6\">\n                {professionalExperiences.map((exp, index) => (\n                  <motion.div\n                    key={exp.id}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}\n                    className=\"relative pl-6 border-l-2 border-emerald-300 dark:border-emerald-700\"\n                  >\n                    <span className=\"text-sm text-muted-foreground\">{exp.period}</span>\n                    <h4 className=\"font-medium mt-1\">{exp.role}</h4>\n                    <p className=\"text-sm font-medium text-emerald-600 dark:text-emerald-400\">{exp.company}</p>\n                    <p className=\"text-sm mt-2 text-muted-foreground\">{exp.description}</p>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n\n            {/* Education */}\n            <div>\n              <h3 className=\"text-2xl font-semibold mb-6\">Education</h3>\n              <div className=\"space-y-6\">\n                {educationalExperiences.map((exp, index) => (\n                  <motion.div\n                    key={exp.id}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}\n                    className=\"relative pl-6 border-l-2 border-blue-300 dark:border-blue-700\"\n                  >\n                    <span className=\"text-sm text-muted-foreground\">{exp.period}</span>\n                    <h4 className=\"font-medium mt-1\">{exp.role}</h4>\n                    <p className=\"text-sm font-medium text-blue-600 dark:text-blue-400\">{exp.company}</p>\n                    <p className=\"text-sm mt-2 text-muted-foreground\">{exp.description}</p>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADA;AAHA;;;;AAsBA,kCAAkC;AAClC,MAAM,UAA2C;IAC/C,oBAAM,6LAAC,qMAAA,CAAA,OAAI;QAAC,WAAU;;;;;;IACtB,sBAAQ,6LAAC,yMAAA,CAAA,SAAM;QAAC,WAAU;;;;;;IAC1B,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IAC9B,sBAAQ,6LAAC,wNAAA,CAAA,SAAM;QAAC,WAAU;;;;;;IAC1B,0BAAY,6LAAC,iNAAA,CAAA,aAAU;QAAC,WAAU;;;;;;IAClC,mBAAK,6LAAC,mMAAA,CAAA,MAAG;QAAC,WAAU;;;;;;IACpB,yBAAW,6LAAC,+MAAA,CAAA,YAAS;QAAC,WAAU;;;;;;IAChC,4BAAc,6LAAC,mNAAA,CAAA,YAAS;QAAC,WAAU;;;;;;IACnC,8BAAgB,6LAAC,8NAAA,CAAA,cAAW;QAAC,WAAU;;;;;;IACvC,2BAAa,6LAAC,iNAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IACjC,sBAAQ,6LAAC,yMAAA,CAAA,SAAM;QAAC,WAAU;;;;;;IAC1B,qBAAO,6LAAC,uMAAA,CAAA,QAAK;QAAC,WAAU;;;;;;IACxB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;QAAC,WAAU;;;;;;IACxB,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IAC9B,2BAAa,6LAAC,iNAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;AACnC;AAEA,qBAAqB;AACrB,MAAM,SAAS;IACb;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAID,6CAA6C;AAC7C,MAAM,0BAA0B;IAC9B;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aACE;IACJ;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aACE;IACJ;CACD;AAED,MAAM,yBAAyB;IAC7B;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aACE;IACJ;CACD;AAED,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aACE;IACJ;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aACE;IACJ;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aACE;IACJ;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aACE;IACJ;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aACE;IACJ;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aACE;IACJ;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aACE;IACJ;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aACE;IACJ;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;wBACX;sCACO,6LAAC;4BAAK,WAAU;sCAAyC;;;;;;;;;;;;8BAIjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAGR,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,6LAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,MAAM,KAAK;sEAAG,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,GAAG,kBAAI,6LAAC,qMAAA,CAAA,OAAI;;;;;;;;;;sEACxE,6LAAC;4DAAG,WAAU;sEAAoB,MAAM,IAAI;;;;;;;;;;;;8DAE9C,6LAAC;oDAAE,WAAU;8DAAiC,MAAM,IAAI;;;;;;;2CAVnD,MAAM,EAAE;;;;;;;;;;;;;;;;sCAkBrB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,KAAK,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAiC,IAAI,MAAM;;;;;;8DAC3D,6LAAC;oDAAG,WAAU;8DAAoB,IAAI,IAAI;;;;;;8DAC1C,6LAAC;oDAAE,WAAU;8DAA4D,IAAI,OAAO;;;;;;8DACpF,6LAAC;oDAAE,WAAU;8DAAsC,IAAI,WAAW;;;;;;;2CAT7D,IAAI,EAAE;;;;;;;;;;;;;;;;sCAgBnB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,6LAAC;oCAAI,WAAU;8CACZ,wBAAwB,GAAG,CAAC,CAAC,KAAK,sBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAiC,IAAI,MAAM;;;;;;8DAC3D,6LAAC;oDAAG,WAAU;8DAAoB,IAAI,IAAI;;;;;;8DAC1C,6LAAC;oDAAE,WAAU;8DAA8D,IAAI,OAAO;;;;;;8DACtF,6LAAC;oDAAE,WAAU;8DAAsC,IAAI,WAAW;;;;;;;2CAT7D,IAAI,EAAE;;;;;;;;;;;;;;;;sCAgBnB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,6LAAC;oCAAI,WAAU;8CACZ,uBAAuB,GAAG,CAAC,CAAC,KAAK,sBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAiC,IAAI,MAAM;;;;;;8DAC3D,6LAAC;oDAAG,WAAU;8DAAoB,IAAI,IAAI;;;;;;8DAC1C,6LAAC;oDAAE,WAAU;8DAAwD,IAAI,OAAO;;;;;;8DAChF,6LAAC;oDAAE,WAAU;8DAAsC,IAAI,WAAW;;;;;;;2CAT7D,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB/B;KA7GwB"}}, {"offset": {"line": 1654, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1660, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 1702, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1708, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,SAAS,sKAAgB,IAAI;AAEnC,MAAM,gBAAgB,sKAAgB,OAAO;AAE7C,MAAM,eAAe,sKAAgB,MAAM;AAE3C,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,8JAAM,UAAU,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,sKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,sKAAgB,KAAK;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,8JAAM,UAAU,OAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,sKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 1853, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1859, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/project-details-modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { ExternalLink, Calendar, Code, Target, Users, Zap, CheckCircle, Brain } from \"lucide-react\"\n\ninterface ProjectDetailsModalProps {\n  project: {\n    id: number\n    title: string\n    description: string\n    tags: string[]\n    demo_url: string\n    code_url: string\n    featured: boolean\n    icon: string\n    year: number\n    status?: string\n  }\n  isOpen: boolean\n  onClose: () => void\n}\n\n// Enhanced project data with additional details\ninterface ProjectDetails {\n  duration: string\n  teamSize: string\n  challenges: string[]\n  features: string[]\n  achievements: string[]\n  techStack: {\n    frontend?: string[]\n    backend?: string[]\n    tools?: string[]\n    deployment?: string[]\n    database?: string[]\n  }\n  skills: string[]\n}\n\nconst getProjectDetails = (projectId: number): Partial<ProjectDetails> => {\n  const projectDetailsMap: Record<number, ProjectDetails> = {\n    1: { // AI-Powered SEO Article Generator\n      duration: \"3 months\",\n      teamSize: \"Solo Project\",\n      challenges: [\"AI Integration\", \"SEO Optimization\", \"Scalable Architecture\", \"Performance Optimization\"],\n      features: [\"AI Content Generation\", \"Keyword Optimization\", \"Multi-language Support\", \"Analytics Dashboard\"],\n      achievements: [\"50% faster content creation\", \"90% SEO score improvement\", \"Multi-tenant architecture\"],\n      techStack: {\n        frontend: [\"React\", \"MUI\", \"Redux Toolkit Query\"],\n        backend: [\"Pocketsflow\", \"VPS\"],\n        deployment: [\"Vercel\", \"Hostinger\"]\n      },\n      skills: [\"Agile Development\", \"Self-management\", \"AI/ML Integration\", \"Full-stack Development\"]\n    },\n    2: { // Real Estate Syndic Management App\n      duration: \"4 months\",\n      teamSize: \"2 developers\",\n      challenges: [\"Offline Functionality\", \"Document Management\", \"Payment Integration\", \"Team Coordination\"],\n      features: [\"Payment Tracking\", \"Document Upload\", \"Push Notifications\", \"Multi-language\"],\n      achievements: [\"100% offline capability\", \"40% faster payment processing\", \"99.9% uptime\"],\n      techStack: {\n        frontend: [\"React Native\", \"Redux\"],\n        backend: [\"Firebase\"],\n        tools: [\"Expo\"]\n      },\n      skills: [\"Agile Methodology\", \"Pair Programming\", \"Cross-platform Development\", \"Team Collaboration\"]\n    },\n    3: { // Sales Facilitator App for Telecom\n      duration: \"6 months\",\n      teamSize: \"3 developers\",\n      challenges: [\"Offline OCR\", \"Modular Architecture\", \"Performance Optimization\", \"CI/CD Pipeline Setup\"],\n      features: [\"Offline OCR\", \"Customer Registration\", \"Sales Analytics\", \"Multi-module Design\", \"Automated Testing\"],\n      achievements: [\"60% faster field operations\", \"95% OCR accuracy\", \"Modular scalability\", \"Zero-downtime deployments\"],\n      techStack: {\n        frontend: [\"Jetpack Compose\"],\n        backend: [\"Android Native\"],\n        tools: [\"Offline OCR SDK\", \"Multi-Module Architecture\", \"Jenkins\", \"Maestro E2E Testing\"]\n      },\n      skills: [\"Agile Development\", \"Cross-functional Communication\", \"CI/CD Implementation\", \"Test Automation\"]\n    },\n    4: { // Web-to-Mobile Migration\n      duration: \"2 months\",\n      teamSize: \"Solo Project\",\n      challenges: [\"Platform Adaptation\", \"Performance Optimization\", \"User Experience\", \"CI/CD Migration\"],\n      features: [\"Cross-platform Compatibility\", \"Native Performance\", \"Responsive Design\", \"Automated Deployment\"],\n      achievements: [\"100% feature parity\", \"50% better performance\", \"Enhanced UX\", \"Seamless CI/CD pipeline\"],\n      techStack: {\n        frontend: [\"React Native\", \"Redux\"],\n        tools: [\"API Integration\", \"Azure DevOps\", \"CI/CD Pipelines\"]\n      },\n      skills: [\"Agile Methodology\", \"Self-directed Learning\", \"DevOps Practices\", \"Platform Migration\"]\n    },\n    5: { // Student Housing Web App\n      duration: \"5 months\",\n      teamSize: \"4 developers\",\n      challenges: [\"Real-time Search\", \"Geolocation\", \"Payment Integration\", \"Team Coordination\"],\n      features: [\"Advanced Filtering\", \"Map Integration\", \"Booking System\", \"User Reviews\"],\n      achievements: [\"500+ active users\", \"95% booking success rate\", \"4.8/5 user rating\"],\n      techStack: {\n        frontend: [\"React\"],\n        backend: [\"Node.js\", \"Express\", \"MongoDB\"],\n        database: [\"MongoDB\"]\n      },\n      skills: [\"Agile Scrum\", \"Team Leadership\", \"Full-stack Development\", \"Stakeholder Communication\"]\n    },\n    6: { // Budget Tracker Mobile App\n      duration: \"3 months\",\n      teamSize: \"Solo Project\",\n      challenges: [\"Offline Storage\", \"Data Synchronization\", \"User Experience\", \"Performance Optimization\"],\n      features: [\"Offline-first\", \"Expense Categorization\", \"Budget Analytics\", \"Export Reports\"],\n      achievements: [\"100% offline functionality\", \"1000+ downloads\", \"4.5/5 app rating\"],\n      techStack: {\n        frontend: [\"React Native\"],\n        database: [\"SQLite\"],\n        tools: [\"Offline Storage\"]\n      },\n      skills: [\"Agile Development\", \"Mobile Development\", \"Data Management\", \"User-centered Design\"]\n    },\n    7: { // Event Organizer App\n      duration: \"4 months\",\n      teamSize: \"3 developers\",\n      challenges: [\"Real-time Matching\", \"Service Provider Network\", \"Event Management\", \"Cross-team Communication\"],\n      features: [\"Service Provider Matching\", \"Event Planning\", \"Real-time Chat\", \"Payment Processing\"],\n      achievements: [\"200+ service providers\", \"150+ successful events\", \"4.7/5 user rating\"],\n      techStack: {\n        frontend: [\"Flutter\"],\n        backend: [\"Spring Boot\", \"Firebase\"],\n        tools: [\"Dart\"]\n      },\n      skills: [\"Agile Methodology\", \"Cross-functional Teams\", \"Mobile Development\", \"Backend Integration\"]\n    }\n  }\n\n  return projectDetailsMap[projectId] || {}\n}\n\nexport default function ProjectDetailsModal({ project, isOpen, onClose }: ProjectDetailsModalProps) {\n  const details = getProjectDetails(project.id)\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-3 text-2xl\">\n            <span className=\"text-3xl\">{project.icon}</span>\n            {project.title}\n            {project.status && (\n              <Badge variant=\"secondary\" className=\"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\">\n                {project.status}\n              </Badge>\n            )}\n          </DialogTitle>\n        </DialogHeader>\n\n        <div className=\"space-y-8\">\n          {/* Project Overview */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"space-y-4\"\n          >\n            <h3 className=\"text-lg font-semibold flex items-center gap-2\">\n              <Target className=\"h-5 w-5 text-amber-600\" />\n              Project Overview\n            </h3>\n            <p className=\"text-muted-foreground leading-relaxed\">{project.description}</p>\n          </motion.div>\n\n          {/* Project Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3, delay: 0.1 }}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-4\"\n          >\n            <div className=\"bg-amber-50 dark:bg-amber-950 p-4 rounded-lg\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <Calendar className=\"h-4 w-4 text-amber-600\" />\n                <span className=\"font-medium\">Timeline</span>\n              </div>\n              <p className=\"text-sm text-muted-foreground\">{details.duration || \"N/A\"}</p>\n              <p className=\"text-xs text-muted-foreground mt-1\">{project.year}</p>\n            </div>\n            <div className=\"bg-blue-50 dark:bg-blue-950 p-4 rounded-lg\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <Users className=\"h-4 w-4 text-blue-600\" />\n                <span className=\"font-medium\">Team Size</span>\n              </div>\n              <p className=\"text-sm text-muted-foreground\">{details.teamSize || \"N/A\"}</p>\n            </div>\n            <div className=\"bg-green-50 dark:bg-green-950 p-4 rounded-lg\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <Code className=\"h-4 w-4 text-green-600\" />\n                <span className=\"font-medium\">Technologies</span>\n              </div>\n              <p className=\"text-sm text-muted-foreground\">{project.tags.length} technologies</p>\n            </div>\n          </motion.div>\n\n          {/* Technology Stack */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3, delay: 0.2 }}\n            className=\"space-y-4\"\n          >\n            <h3 className=\"text-lg font-semibold flex items-center gap-2\">\n              <Code className=\"h-5 w-5 text-blue-600\" />\n              Technology Stack\n            </h3>\n            <div className=\"flex flex-wrap gap-2\">\n              {project.tags.map((tag) => (\n                <Badge\n                  key={tag}\n                  variant=\"secondary\"\n                  className=\"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\"\n                >\n                  {tag}\n                </Badge>\n              ))}\n            </div>\n\n            {details.techStack && (\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4\">\n                {details.techStack.frontend && (\n                  <div>\n                    <h4 className=\"font-medium text-sm mb-2\">Frontend</h4>\n                    <div className=\"space-y-1\">\n                      {details.techStack.frontend.map((tech: string) => (\n                        <div key={tech} className=\"text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded\">\n                          {tech}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n                {details.techStack.backend && (\n                  <div>\n                    <h4 className=\"font-medium text-sm mb-2\">Backend</h4>\n                    <div className=\"space-y-1\">\n                      {details.techStack.backend.map((tech: string) => (\n                        <div key={tech} className=\"text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded\">\n                          {tech}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n                {(details.techStack.tools || details.techStack.deployment) && (\n                  <div>\n                    <h4 className=\"font-medium text-sm mb-2\">Tools & Deployment</h4>\n                    <div className=\"space-y-1\">\n                      {(details.techStack.tools || details.techStack.deployment || []).map((tech: string) => (\n                        <div key={tech} className=\"text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded\">\n                          {tech}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n          </motion.div>\n\n          {/* Key Features */}\n          {details.features && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: 0.3 }}\n              className=\"space-y-4\"\n            >\n              <h3 className=\"text-lg font-semibold flex items-center gap-2\">\n                <Zap className=\"h-5 w-5 text-purple-600\" />\n                Key Features\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2\">\n                {details.features.map((feature: string, index: number) => (\n                  <div key={index} className=\"flex items-center gap-2\">\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                    <span className=\"text-sm\">{feature}</span>\n                  </div>\n                ))}\n              </div>\n            </motion.div>\n          )}\n\n          {/* Achievements */}\n          {details.achievements && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: 0.4 }}\n              className=\"space-y-4\"\n            >\n              <h3 className=\"text-lg font-semibold flex items-center gap-2\">\n                <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                Key Achievements\n              </h3>\n              <div className=\"grid grid-cols-1 gap-2\">\n                {details.achievements.map((achievement: string, index: number) => (\n                  <div key={index} className=\"flex items-center gap-2 bg-green-50 dark:bg-green-950 p-3 rounded-lg\">\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                    <span className=\"text-sm font-medium\">{achievement}</span>\n                  </div>\n                ))}\n              </div>\n            </motion.div>\n          )}\n\n          {/* Skills & Methodologies */}\n          {details.skills && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: 0.5 }}\n              className=\"space-y-4\"\n            >\n              <h3 className=\"text-lg font-semibold flex items-center gap-2\">\n                <Brain className=\"h-5 w-5 text-indigo-600\" />\n                Skills & Methodologies\n              </h3>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n                {details.skills.map((skill: string, index: number) => (\n                  <div key={index} className=\"bg-indigo-50 dark:bg-indigo-950 p-3 rounded-lg text-center\">\n                    <span className=\"text-sm font-medium text-indigo-800 dark:text-indigo-200\">{skill}</span>\n                  </div>\n                ))}\n              </div>\n            </motion.div>\n          )}\n\n          {/* Action Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3, delay: 0.6 }}\n            className=\"flex gap-4 pt-4 border-t\"\n          >\n            <Button asChild className=\"flex-1\">\n              <a href={project.demo_url} target=\"_blank\" rel=\"noopener noreferrer\">\n                <ExternalLink className=\"mr-2 h-4 w-4\" />\n                Live Demo\n              </a>\n            </Button>\n            <Button asChild variant=\"outline\" className=\"flex-1\">\n              <a href={project.code_url} target=\"_blank\" rel=\"noopener noreferrer\">\n                <Code className=\"mr-2 h-4 w-4\" />\n                View Code\n              </a>\n            </Button>\n          </motion.div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAHA;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AA0CA,MAAM,oBAAoB,CAAC;IACzB,MAAM,oBAAoD;QACxD,GAAG;YACD,UAAU;YACV,UAAU;YACV,YAAY;gBAAC;gBAAkB;gBAAoB;gBAAyB;aAA2B;YACvG,UAAU;gBAAC;gBAAyB;gBAAwB;gBAA0B;aAAsB;YAC5G,cAAc;gBAAC;gBAA+B;gBAA6B;aAA4B;YACvG,WAAW;gBACT,UAAU;oBAAC;oBAAS;oBAAO;iBAAsB;gBACjD,SAAS;oBAAC;oBAAe;iBAAM;gBAC/B,YAAY;oBAAC;oBAAU;iBAAY;YACrC;YACA,QAAQ;gBAAC;gBAAqB;gBAAmB;gBAAqB;aAAyB;QACjG;QACA,GAAG;YACD,UAAU;YACV,UAAU;YACV,YAAY;gBAAC;gBAAyB;gBAAuB;gBAAuB;aAAoB;YACxG,UAAU;gBAAC;gBAAoB;gBAAmB;gBAAsB;aAAiB;YACzF,cAAc;gBAAC;gBAA2B;gBAAiC;aAAe;YAC1F,WAAW;gBACT,UAAU;oBAAC;oBAAgB;iBAAQ;gBACnC,SAAS;oBAAC;iBAAW;gBACrB,OAAO;oBAAC;iBAAO;YACjB;YACA,QAAQ;gBAAC;gBAAqB;gBAAoB;gBAA8B;aAAqB;QACvG;QACA,GAAG;YACD,UAAU;YACV,UAAU;YACV,YAAY;gBAAC;gBAAe;gBAAwB;gBAA4B;aAAuB;YACvG,UAAU;gBAAC;gBAAe;gBAAyB;gBAAmB;gBAAuB;aAAoB;YACjH,cAAc;gBAAC;gBAA+B;gBAAoB;gBAAuB;aAA4B;YACrH,WAAW;gBACT,UAAU;oBAAC;iBAAkB;gBAC7B,SAAS;oBAAC;iBAAiB;gBAC3B,OAAO;oBAAC;oBAAmB;oBAA6B;oBAAW;iBAAsB;YAC3F;YACA,QAAQ;gBAAC;gBAAqB;gBAAkC;gBAAwB;aAAkB;QAC5G;QACA,GAAG;YACD,UAAU;YACV,UAAU;YACV,YAAY;gBAAC;gBAAuB;gBAA4B;gBAAmB;aAAkB;YACrG,UAAU;gBAAC;gBAAgC;gBAAsB;gBAAqB;aAAuB;YAC7G,cAAc;gBAAC;gBAAuB;gBAA0B;gBAAe;aAA0B;YACzG,WAAW;gBACT,UAAU;oBAAC;oBAAgB;iBAAQ;gBACnC,OAAO;oBAAC;oBAAmB;oBAAgB;iBAAkB;YAC/D;YACA,QAAQ;gBAAC;gBAAqB;gBAA0B;gBAAoB;aAAqB;QACnG;QACA,GAAG;YACD,UAAU;YACV,UAAU;YACV,YAAY;gBAAC;gBAAoB;gBAAe;gBAAuB;aAAoB;YAC3F,UAAU;gBAAC;gBAAsB;gBAAmB;gBAAkB;aAAe;YACrF,cAAc;gBAAC;gBAAqB;gBAA4B;aAAoB;YACpF,WAAW;gBACT,UAAU;oBAAC;iBAAQ;gBACnB,SAAS;oBAAC;oBAAW;oBAAW;iBAAU;gBAC1C,UAAU;oBAAC;iBAAU;YACvB;YACA,QAAQ;gBAAC;gBAAe;gBAAmB;gBAA0B;aAA4B;QACnG;QACA,GAAG;YACD,UAAU;YACV,UAAU;YACV,YAAY;gBAAC;gBAAmB;gBAAwB;gBAAmB;aAA2B;YACtG,UAAU;gBAAC;gBAAiB;gBAA0B;gBAAoB;aAAiB;YAC3F,cAAc;gBAAC;gBAA8B;gBAAmB;aAAmB;YACnF,WAAW;gBACT,UAAU;oBAAC;iBAAe;gBAC1B,UAAU;oBAAC;iBAAS;gBACpB,OAAO;oBAAC;iBAAkB;YAC5B;YACA,QAAQ;gBAAC;gBAAqB;gBAAsB;gBAAmB;aAAuB;QAChG;QACA,GAAG;YACD,UAAU;YACV,UAAU;YACV,YAAY;gBAAC;gBAAsB;gBAA4B;gBAAoB;aAA2B;YAC9G,UAAU;gBAAC;gBAA6B;gBAAkB;gBAAkB;aAAqB;YACjG,cAAc;gBAAC;gBAA0B;gBAA0B;aAAoB;YACvF,WAAW;gBACT,UAAU;oBAAC;iBAAU;gBACrB,SAAS;oBAAC;oBAAe;iBAAW;gBACpC,OAAO;oBAAC;iBAAO;YACjB;YACA,QAAQ;gBAAC;gBAAqB;gBAA0B;gBAAsB;aAAsB;QACtG;IACF;IAEA,OAAO,iBAAiB,CAAC,UAAU,IAAI,CAAC;AAC1C;AAEe,SAAS,oBAAoB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAA4B;IAChG,MAAM,UAAU,kBAAkB,QAAQ,EAAE;IAE5C,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,8HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAK,WAAU;0CAAY,QAAQ,IAAI;;;;;;4BACvC,QAAQ,KAAK;4BACb,QAAQ,MAAM,kBACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,QAAQ,MAAM;;;;;;;;;;;;;;;;;8BAMvB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAA2B;;;;;;;8CAG/C,6LAAC;oCAAE,WAAU;8CAAyC,QAAQ,WAAW;;;;;;;;;;;;sCAI3E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,6LAAC;4CAAE,WAAU;sDAAiC,QAAQ,QAAQ,IAAI;;;;;;sDAClE,6LAAC;4CAAE,WAAU;sDAAsC,QAAQ,IAAI;;;;;;;;;;;;8CAEjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,6LAAC;4CAAE,WAAU;sDAAiC,QAAQ,QAAQ,IAAI;;;;;;;;;;;;8CAEpE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,6LAAC;4CAAE,WAAU;;gDAAiC,QAAQ,IAAI,CAAC,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAKtE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAG5C,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,oBACjB,6LAAC,6HAAA,CAAA,QAAK;4CAEJ,SAAQ;4CACR,WAAU;sDAET;2CAJI;;;;;;;;;;gCASV,QAAQ,SAAS,kBAChB,6LAAC;oCAAI,WAAU;;wCACZ,QAAQ,SAAS,CAAC,QAAQ,kBACzB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2B;;;;;;8DACzC,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,qBAC/B,6LAAC;4DAAe,WAAU;sEACvB;2DADO;;;;;;;;;;;;;;;;wCAOjB,QAAQ,SAAS,CAAC,OAAO,kBACxB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2B;;;;;;8DACzC,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,qBAC9B,6LAAC;4DAAe,WAAU;sEACvB;2DADO;;;;;;;;;;;;;;;;wCAOjB,CAAC,QAAQ,SAAS,CAAC,KAAK,IAAI,QAAQ,SAAS,CAAC,UAAU,mBACvD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2B;;;;;;8DACzC,6LAAC;oDAAI,WAAU;8DACZ,CAAC,QAAQ,SAAS,CAAC,KAAK,IAAI,QAAQ,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,qBACpE,6LAAC;4DAAe,WAAU;sEACvB;2DADO;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAYvB,QAAQ,QAAQ,kBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAA4B;;;;;;;8CAG7C,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAiB,sBACtC,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAK,WAAU;8DAAW;;;;;;;2CAFnB;;;;;;;;;;;;;;;;wBAUjB,QAAQ,YAAY,kBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAA2B;;;;;;;8CAGpD,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,aAAqB,sBAC9C,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAK,WAAU;8DAAuB;;;;;;;2CAF/B;;;;;;;;;;;;;;;;wBAUjB,QAAQ,MAAM,kBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAA4B;;;;;;;8CAG/C,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAe,sBAClC,6LAAC;4CAAgB,WAAU;sDACzB,cAAA,6LAAC;gDAAK,WAAU;0DAA4D;;;;;;2CADpE;;;;;;;;;;;;;;;;sCASlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,8HAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,WAAU;8CACxB,cAAA,6LAAC;wCAAE,MAAM,QAAQ,QAAQ;wCAAE,QAAO;wCAAS,KAAI;;0DAC7C,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAI7C,6LAAC,8HAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAU,WAAU;8CAC1C,cAAA,6LAAC;wCAAE,MAAM,QAAQ,QAAQ;wCAAE,QAAO;wCAAS,KAAI;;0DAC7C,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;KA7NwB"}}, {"offset": {"line": 2865, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2871, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/portfolio-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { motion } from \"framer-motion\"\nimport { ExternalLink, Code, ArrowRight, Info } from \"lucide-react\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Button } from \"@/components/ui/button\"\nimport ProjectDetailsModal from \"@/components/project-details-modal\"\n\n// Static projects data organized by year\nconst projectsByYear = {\n  2025: [\n    {\n      id: 1,\n      title: \"AI-Powered SEO Article Generator (SaaS)\",\n      description: \"SaaS platform for generating SEO-optimized articles using AI to support content creators and digital marketers. Automated content creation with customizable prompts and keyword optimization — currently being developed for production deployment to support efficient content scaling for marketing teams.\",\n      tags: [\"React\", \"MUI\", \"Redux Toolkit Query\", \"Supabase\", \"Pocketsflow\", \"VPS\", \"Vercel\", \"Hostinger\"],\n      demo_url: \"https://seo-generator-demo.vercel.app\",\n      code_url: \"https://github.com/apachi1444/ai-seo-generator\",\n      featured: true,\n      icon: \"✍️\",\n      status: \"In Development\",\n      year: 2025\n    },\n    {\n      id: 9,\n      title: \"React Native Scalable Drawer Library\",\n      description: \"Open source React Native library providing customizable and performant drawer components for mobile applications. Designed to offer developers flexible drawer solutions with smooth animations, gesture support, and extensive customization options for enhanced user experience.\",\n      tags: [\"React Native\", \"TypeScript\", \"Expo\", \"Open Source\", \"NPM Package\", \"Mobile UI\"],\n      demo_url: \"https://react-native-scalable-drawer-demo.expo.dev\",\n      code_url: \"https://github.com/apachi1444/react-native-scalable-drawer\",\n      featured: true,\n      icon: \"📚\",\n      status: \"In Development\",\n      year: 2025\n    },\n    {\n      id: 10,\n      title: \"TrackMe - Habit Tracking Mobile App\",\n      description: \"Mobile application for habit tracking and personal productivity enhancement. Features intuitive habit creation, progress tracking, analytics, and motivational insights to help users build and maintain positive habits. Built with modern mobile development practices for optimal user experience.\",\n      tags: [\"Expo\", \"React Native\", \"TypeScript\", \"Mobile App\", \"Productivity\", \"Analytics\"],\n      demo_url: \"https://trackme-habits-demo.expo.dev\",\n      code_url: \"https://github.com/apachi1444/trackme-habits\",\n      featured: true,\n      icon: \"📈\",\n      status: \"In Development\",\n      year: 2025\n    }\n  ],\n  2024: [\n    {\n      id: 11,\n      title: \"Sales Facilitator App for Telecom Client\",\n      description: \"Ongoing development of internal Android application to assist field agents with telecom product sales and customer registration. Continuously enhancing offline mode with remote server synchronization, Firebase push notifications, and E2E testing with Maestro. Regular improvements through OCR solutions optimization and multi-module architecture refinements.\",\n      tags: [\"Jetpack Compose\", \"Android Native\", \"OCR SDK\", \"Firebase\", \"Offline Sync\", \"Maestro E2E\", \"Jenkins CI/CD\"],\n      demo_url: \"https://telecom-sales-demo.vercel.app\",\n      code_url: \"https://github.com/apachi1444/telecom-sales-app\",\n      featured: true,\n      icon: \"📱\",\n      status: \"Ongoing\",\n      year: 2024\n    },\n    {\n      id: 12,\n      title: \"Real Estate Syndic Management App (Mobile)\",\n      description: \"Mobile solution for managing payments and communications related to building syndicates. Developed syndic operations with features for payment tracking, document uploads, and notifications — bringing transparency and ease to property managers and residents.\",\n      tags: [\"React Native\", \"Firebase\", \"Redux\", \"Expo\", \"Push Notifications\"],\n      demo_url: \"https://syndic-app-demo.netlify.app\",\n      code_url: \"https://github.com/apachi1444/syndic-management-app\",\n      featured: true,\n      icon: \"🏢\",\n      status: \"Completed\",\n      year: 2024\n    },\n    {\n      id: 13,\n      title: \"MovieMatch - QR Code Integration (Contribution)\",\n      description: \"Contributed QR code technology integration to MovieMatch, a Tinder-like app for choosing films between two people. Implemented QR code functionality allowing guests to easily join host sessions by scanning codes, enhancing user experience and session accessibility for collaborative movie selection.\",\n      tags: [\"Android\", \"Jetpack Compose\", \"QR Code\", \"Kotlin\", \"Collaboration\", \"Mobile UI\"],\n      demo_url: \"https://moviematch-demo.vercel.app\",\n      code_url: \"https://github.com/friend-username/moviematch-app\",\n      featured: true,\n      icon: \"🎬\",\n      status: \"Contribution\",\n      year: 2024\n    }\n  ],\n  2023: [\n    {\n      id: 4,\n      title: \"Web-to-Mobile Migration Project\",\n      description: \"Migrated an existing web app to mobile to broaden user reach and improve accessibility. Seamless mobile adaptation of a previously web-only platform, enhancing user engagement on smartphones and tablets.\",\n      tags: [\"React Native\", \"Redux\", \"API Integration\"],\n      demo_url: \"https://mobile-migration-demo.netlify.app\",\n      code_url: \"https://github.com/apachi1444/web-to-mobile-migration\",\n      featured: false,\n      icon: \"🔄\",\n      year: 2023\n    }\n  ],\n  2022: [\n    {\n      id: 5,\n      title: \"Student Housing Web App (Marrakech)\",\n      description: \"Web platform for students to easily find and rent apartments in Marrakech. Provided a streamlined rental system with filtering and user-friendly listings, simplifying the housing search for students.\",\n      tags: [\"MongoDB\", \"Express\", \"React\", \"Node.js\"],\n      demo_url: \"https://student-housing-marrakech.vercel.app\",\n      code_url: \"https://github.com/apachi1444/student-housing-marrakech\",\n      featured: true,\n      icon: \"🏠\",\n      year: 2022\n    },\n    {\n      id: 6,\n      title: \"Budget Tracker Mobile App\",\n      description: \"Offline-first mobile application to help users manage daily expenses. Empowered users to track budgets without internet dependency, enhancing accessibility and usability for personal finance.\",\n      tags: [\"React Native\", \"SQLite\", \"Offline Storage\"],\n      demo_url: \"https://budget-tracker-demo.netlify.app\",\n      code_url: \"https://github.com/apachi1444/budget-tracker-mobile\",\n      featured: true,\n      icon: \"💸\",\n      year: 2022\n    },\n    {\n      id: 7,\n      title: \"Event Organizer App\",\n      description: \"Mobile app connecting event organizers with service providers (singers, chefs, decorators, etc.). Facilitated professional connections and simplified event planning for users, from private parties to large gatherings.\",\n      tags: [\"Flutter\", \"Firebase\", \"Spring Boot\", \"Dart\"],\n      demo_url: \"https://event-organizer-demo.vercel.app\",\n      code_url: \"https://github.com/apachi1444/event-organizer-app\",\n      featured: true,\n      icon: \"🎉\",\n      year: 2022\n    }\n  ]\n}\n\n// Define project type for better TypeScript support\ninterface Project {\n  id: number\n  title: string\n  description: string\n  tags: string[]\n  demo_url: string\n  code_url: string\n  featured: boolean\n  icon: string\n  year: number\n  status?: string\n}\n\n// Function to escape apostrophes in a string\nfunction escapeApostrophes(str: string): string {\n  return str.replace(/'/g, \"&apos;\")\n}\n\nexport default function PortfolioSection() {\n  const [selectedProject, setSelectedProject] = useState<Project | null>(null)\n  const [isModalOpen, setIsModalOpen] = useState(false)\n\n  const openProjectModal = (project: Project) => {\n    setSelectedProject(project)\n    setIsModalOpen(true)\n  }\n\n  const closeProjectModal = () => {\n    setIsModalOpen(false)\n    setSelectedProject(null)\n  }\n\n  const container = {\n    hidden: { opacity: 0 },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  }\n\n  const item = {\n    hidden: { opacity: 0, y: 20 },\n    show: { opacity: 1, y: 0 },\n  }\n\n  return (\n    <div id=\"portfolio\" className=\"min-h-screen p-8 md:p-12 bg-gradient-to-br from-amber-50 to-orange-100 dark:from-gray-900 dark:to-amber-950\">\n      <div className=\"max-w-6xl mx-auto\">\n        <motion.h2\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"text-4xl md:text-5xl font-bold mb-6 text-center lg:text-left\"\n        >\n          My <span className=\"text-amber-600 dark:text-amber-400\">Portfolio</span>\n        </motion.h2>\n\n        <motion.p\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.1 }}\n          className=\"text-lg text-muted-foreground mb-12 text-center lg:text-left\"\n        >\n          A chronological journey through my projects and achievements\n        </motion.p>\n\n        {/* Projects organized by year */}\n        <div className=\"space-y-16\">\n          {Object.entries(projectsByYear)\n            .sort(([a], [b]) => Number(b) - Number(a)) // Sort years in descending order\n            .map(([year, yearProjects]) => (\n              <motion.div\n                key={year}\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"space-y-8\"\n              >\n                {/* Year Header */}\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"text-3xl md:text-4xl font-bold text-amber-600 dark:text-amber-400\">\n                    🚀 {year}\n                  </div>\n                  <div className=\"flex-1 h-px bg-gradient-to-r from-amber-300 to-transparent dark:from-amber-600\"></div>\n                </div>\n\n                {/* Projects Grid for this year */}\n                <motion.div\n                  variants={container}\n                  initial=\"hidden\"\n                  animate=\"show\"\n                  className=\"grid grid-cols-1 md:grid-cols-2 gap-8\"\n                >\n                  {yearProjects.map((project) => (\n                    <motion.div\n                      key={project.id}\n                      variants={item}\n                      className=\"group relative overflow-hidden rounded-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300\"\n                    >\n                      <div className=\"relative aspect-video overflow-hidden bg-gradient-to-br from-amber-100 to-orange-200 dark:from-amber-900 dark:to-orange-900\">\n                        {/* Project Icon */}\n                        <div className=\"absolute inset-0 flex items-center justify-center\">\n                          <div className=\"text-6xl opacity-20\">{project.icon}</div>\n                        </div>\n\n                        {/* Status Badge */}\n                        {'status' in project && project.status && (\n                          <div className=\"absolute top-4 right-4\">\n                            <Badge variant=\"secondary\" className=\"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\">\n                              {project.status}\n                            </Badge>\n                          </div>\n                        )}\n\n                        {/* Hover Overlay */}\n                        <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                          <div className=\"absolute bottom-0 left-0 right-0 p-6\">\n                            <div className=\"flex space-x-2\">\n                              <Button asChild size=\"sm\" variant=\"default\" className=\"rounded-full flex-1\">\n                                <a href={project.demo_url} target=\"_blank\" rel=\"noopener noreferrer\">\n                                  <ExternalLink className=\"mr-2 h-4 w-4\" />\n                                  Demo\n                                </a>\n                              </Button>\n                              <Button\n                                asChild\n                                size=\"sm\"\n                                variant=\"outline\"\n                                className=\"rounded-full bg-white/20 border-white/40 text-white hover:bg-white/30 flex-1\"\n                              >\n                                <a href={project.code_url} target=\"_blank\" rel=\"noopener noreferrer\">\n                                  <Code className=\"mr-2 h-4 w-4\" />\n                                  Code\n                                </a>\n                              </Button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"p-6\">\n                        <div className=\"flex items-start gap-3 mb-2\">\n                          <span className=\"text-2xl\">{project.icon}</span>\n                          <h3 className=\"text-xl font-semibold flex-1\">{project.title}</h3>\n                        </div>\n                        <p className=\"text-muted-foreground mb-4 text-sm leading-relaxed line-clamp-3\">{escapeApostrophes(project.description)}</p>\n                        <div className=\"flex flex-wrap gap-2 mb-4\">\n                          {project.tags.slice(0, 3).map((tag) => (\n                            <Badge\n                              key={tag}\n                              variant=\"secondary\"\n                              className=\"bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200 text-xs\"\n                            >\n                              {tag}\n                            </Badge>\n                          ))}\n                          {project.tags.length > 3 && (\n                            <Badge variant=\"secondary\" className=\"bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 text-xs\">\n                              +{project.tags.length - 3} more\n                            </Badge>\n                          )}\n                        </div>\n                        <Button\n                          onClick={() => openProjectModal(project)}\n                          variant=\"outline\"\n                          size=\"sm\"\n                          className=\"w-full rounded-full group\"\n                        >\n                          <Info className=\"mr-2 h-4 w-4\" />\n                          View Details\n                          <ArrowRight className=\"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\" />\n                        </Button>\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </motion.div>\n            ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"mt-12 text-center\"\n        >\n          <Button variant=\"outline\" size=\"lg\" className=\"rounded-full group\">\n            View All Projects\n            <ArrowRight className=\"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\" />\n          </Button>\n        </motion.div>\n      </div>\n\n      {/* Project Details Modal */}\n      {selectedProject && (\n        <ProjectDetailsModal\n          project={selectedProject}\n          isOpen={isModalOpen}\n          onClose={closeProjectModal}\n        />\n      )}\n    </div>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AACA;AACA;AAJA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;;;;AASA,yCAAyC;AACzC,MAAM,iBAAiB;IACrB,MAAM;QACJ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAS;gBAAO;gBAAuB;gBAAY;gBAAe;gBAAO;gBAAU;aAAY;YACtG,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAgB;gBAAc;gBAAQ;gBAAe;gBAAe;aAAY;YACvF,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAQ;gBAAgB;gBAAc;gBAAc;gBAAgB;aAAY;YACvF,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,MAAM;QACR;KACD;IACD,MAAM;QACJ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAmB;gBAAkB;gBAAW;gBAAY;gBAAgB;gBAAe;aAAgB;YAClH,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAgB;gBAAY;gBAAS;gBAAQ;aAAqB;YACzE,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAW;gBAAmB;gBAAW;gBAAU;gBAAiB;aAAY;YACvF,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,MAAM;QACR;KACD;IACD,MAAM;QACJ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAgB;gBAAS;aAAkB;YAClD,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;YACN,MAAM;QACR;KACD;IACD,MAAM;QACJ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAW;gBAAW;gBAAS;aAAU;YAChD,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAgB;gBAAU;aAAkB;YACnD,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAW;gBAAY;gBAAe;aAAO;YACpD,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;YACN,MAAM;QACR;KACD;AACH;AAgBA,6CAA6C;AAC7C,SAAS,kBAAkB,GAAW;IACpC,OAAO,IAAI,OAAO,CAAC,MAAM;AAC3B;AAEe,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB,CAAC;QACxB,mBAAmB;QACnB,eAAe;IACjB;IAEA,MAAM,oBAAoB;QACxB,eAAe;QACf,mBAAmB;IACrB;IAEA,MAAM,YAAY;QAChB,QAAQ;YAAE,SAAS;QAAE;QACrB,MAAM;YACJ,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,OAAO;QACX,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG;QAAE;IAC3B;IAEA,qBACE,6LAAC;QAAI,IAAG;QAAY,WAAU;;0BAC5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;4BACX;0CACI,6LAAC;gCAAK,WAAU;0CAAqC;;;;;;;;;;;;kCAG1D,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCACX;;;;;;kCAKD,6LAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,gBACb,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAK,OAAO,KAAK,OAAO,IAAI,iCAAiC;yBAC3E,GAAG,CAAC,CAAC,CAAC,MAAM,aAAa,iBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDAAoE;oDAC7E;;;;;;;0DAEN,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,UAAU;wCACV,SAAQ;wCACR,SAAQ;wCACR,WAAU;kDAET,aAAa,GAAG,CAAC,CAAC,wBACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,UAAU;gDACV,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EAAuB,QAAQ,IAAI;;;;;;;;;;;4DAInD,YAAY,WAAW,QAAQ,MAAM,kBACpC,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAClC,QAAQ,MAAM;;;;;;;;;;;0EAMrB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,8HAAA,CAAA,SAAM;gFAAC,OAAO;gFAAC,MAAK;gFAAK,SAAQ;gFAAU,WAAU;0FACpD,cAAA,6LAAC;oFAAE,MAAM,QAAQ,QAAQ;oFAAE,QAAO;oFAAS,KAAI;;sGAC7C,6LAAC,yNAAA,CAAA,eAAY;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;0FAI7C,6LAAC,8HAAA,CAAA,SAAM;gFACL,OAAO;gFACP,MAAK;gFACL,SAAQ;gFACR,WAAU;0FAEV,cAAA,6LAAC;oFAAE,MAAM,QAAQ,QAAQ;oFAAE,QAAO;oFAAS,KAAI;;sGAC7C,6LAAC,qMAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAS7C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAY,QAAQ,IAAI;;;;;;kFACxC,6LAAC;wEAAG,WAAU;kFAAgC,QAAQ,KAAK;;;;;;;;;;;;0EAE7D,6LAAC;gEAAE,WAAU;0EAAmE,kBAAkB,QAAQ,WAAW;;;;;;0EACrH,6LAAC;gEAAI,WAAU;;oEACZ,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC7B,6LAAC,6HAAA,CAAA,QAAK;4EAEJ,SAAQ;4EACR,WAAU;sFAET;2EAJI;;;;;oEAOR,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrB,6LAAC,6HAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;;4EAAwE;4EACzG,QAAQ,IAAI,CAAC,MAAM,GAAG;4EAAE;;;;;;;;;;;;;0EAIhC,6LAAC,8HAAA,CAAA,SAAM;gEACL,SAAS,IAAM,iBAAiB;gEAChC,SAAQ;gEACR,MAAK;gEACL,WAAU;;kFAEV,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;kFAEjC,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;+CA3ErB,QAAQ,EAAE;;;;;;;;;;;+BAvBhB;;;;;;;;;;kCA4Gb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,WAAU;;gCAAqB;8CAEjE,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAM3B,iCACC,6LAAC,6IAAA,CAAA,UAAmB;gBAClB,SAAS;gBACT,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAKnB;GA3LwB;KAAA"}}, {"offset": {"line": 3539, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3545, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/awards-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport {\n  Code,\n  Server,\n  Database,\n  Layout,\n  Zap,\n  Lightbulb,\n  Smartphone,\n  GitBranch,\n  CheckCircle,\n  GitMerge,\n  Kanban,\n  Brain,\n  Users,\n  Settings,\n  TestTube,\n} from \"lucide-react\"\nimport { JSX } from \"react\"\n\n// Map of icon names to components\nconst iconMap: Record<string, JSX.Element> = {\n  code: <Code className=\"w-6 h-6\" />,\n  server: <Server className=\"w-6 h-6\" />,\n  database: <Database className=\"w-6 h-6\" />,\n  layout: <Layout className=\"w-6 h-6\" />,\n  zap: <Zap className=\"w-6 h-6\" />,\n  lightbulb: <Lightbulb className=\"w-6 h-6\" />,\n  smartphone: <Smartphone className=\"w-6 h-6\" />,\n  \"git-branch\": <GitBranch className=\"w-6 h-6\" />,\n  \"check-circle\": <CheckCircle className=\"w-6 h-6\" />,\n  \"git-merge\": <GitMerge className=\"w-6 h-6\" />,\n  trello: <Kanban className=\"w-6 h-6\" />,\n  brain: <Brain className=\"w-6 h-6\" />,\n  users: <Users className=\"w-6 h-6\" />,\n  settings: <Settings className=\"w-6 h-6\" />,\n  \"test-tube\": <TestTube className=\"w-6 h-6\" />,\n}\n\n// Static awards data\nconst awards = [\n  {\n    id: 1,\n    year: \"2025\",\n    title: \"Technical Workshop Host\",\n    subtitle: \"From React to React Native\",\n    description: \"Delivered a hands-on workshop to guide web developers transitioning from React.js to mobile development using React Native.\",\n    icon: \"users\",\n    color: \"text-blue-500\",\n    category: \"Technical Leadership\",\n  },\n  {\n    id: 2,\n    year: \"2024\",\n    title: \"Technical Writer\",\n    subtitle: \"Medium\",\n    description: \"Authored two articles sharing my transition from Student to Mobile developer on Medium platform.\",\n    icon: \"lightbulb\",\n    color: \"text-green-500\",\n    category: \"Content Creation\",\n  },\n  {\n    id: 3,\n    year: \"2024\",\n    title: \"Open Source Contributor\",\n    subtitle: \"Analytiks SDK\",\n    description: \"Contributed to the development of a public analytics SDK, enhancing logging and data capture features for Android apps.\",\n    icon: \"code\",\n    color: \"text-purple-500\",\n    category: \"Open Source\",\n  },\n  {\n    id: 4,\n    year: \"2022\",\n    title: \"Project Leader\",\n    subtitle: \"Student Housing Rental Web App\",\n    description: \"Led the full-stack development and team coordination of a MERN-based web application for student apartment rentals in Marrakech.\",\n    icon: \"trello\",\n    color: \"text-orange-500\",\n    category: \"Project Management\",\n  },\n  {\n    id: 5,\n    year: \"2021\",\n    title: \"Campus Director\",\n    subtitle: \"Hult Prize ENSA\",\n    description: \"Organized and led the local edition of the global Hult Prize social entrepreneurship competition, mentoring student teams and coordinating with international representatives.\",\n    icon: \"users\",\n    color: \"text-emerald-500\",\n    category: \"Leadership\",\n  },\n]\n\nexport default function AwardsSection() {\n  const container = {\n    hidden: { opacity: 0 },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  }\n\n  const item = {\n    hidden: { opacity: 0, y: 20 },\n    show: { opacity: 1, y: 0 },\n  }\n\n  return (\n    <div className=\"min-h-screen p-8 md:p-12 bg-gradient-to-br from-amber-50 to-orange-100 dark:from-gray-900 dark:to-amber-950\">\n      <div className=\"max-w-6xl mx-auto\">\n        <motion.h2\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"text-4xl md:text-5xl font-bold mb-6 text-center lg:text-left\"\n        >\n          Awards & <span className=\"text-amber-600 dark:text-amber-400\">Recognition</span>\n        </motion.h2>\n\n        <motion.p\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.1 }}\n          className=\"text-lg text-muted-foreground mb-12 text-center lg:text-left\"\n        >\n          Achievements, contributions, and recognition in the developer community\n        </motion.p>\n\n        {/* Awards Grid */}\n        <motion.div\n          variants={container}\n          initial=\"hidden\"\n          animate=\"show\"\n          className=\"grid grid-cols-1 md:grid-cols-2 gap-8\"\n        >\n          {awards.map((award) => (\n            <motion.div\n              key={award.id}\n              variants={item}\n              className=\"group relative overflow-hidden rounded-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300\"\n            >\n              <div className=\"p-8\">\n                <div className=\"flex items-start gap-6\">\n                  <div className={`${award.color} mt-2 group-hover:scale-110 transition-transform duration-300`}>\n                    {iconMap[award.icon.toLowerCase()] || <Code className=\"w-6 h-6\" />}\n                  </div>\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-3 mb-3\">\n                      <span className=\"text-xs bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200 px-3 py-1 rounded-full font-medium\">\n                        {award.year}\n                      </span>\n                      <span className=\"text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 px-3 py-1 rounded-full\">\n                        {award.category}\n                      </span>\n                    </div>\n                    <h3 className=\"text-xl font-bold mb-2 group-hover:text-amber-600 dark:group-hover:text-amber-400 transition-colors duration-300\">\n                      {award.title}\n                    </h3>\n                    <p className=\"text-sm font-semibold text-amber-600 dark:text-amber-400 mb-4\">\n                      {award.subtitle}\n                    </p>\n                    <p className=\"text-muted-foreground leading-relaxed\">\n                      {award.description}\n                    </p>\n                  </div>\n                </div>\n              </div>\n              \n              {/* Decorative border */}\n              <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-500 to-orange-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left\"></div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-8 border border-amber-200 dark:border-amber-800\">\n            <h3 className=\"text-2xl font-bold mb-4\">Interested in Collaboration?</h3>\n            <p className=\"text-muted-foreground mb-6\">\n              I m always open to new opportunities, technical discussions, and knowledge sharing.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"https://medium.com/@yessinejawa\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center justify-center px-6 py-3 bg-amber-600 hover:bg-amber-700 text-white rounded-full font-medium transition-colors duration-300\"\n              >\n                Read My Articles\n              </a>\n              <a\n                href=\"https://github.com/apachi1444\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center justify-center px-6 py-3 border border-amber-600 text-amber-600 hover:bg-amber-600 hover:text-white rounded-full font-medium transition-colors duration-300\"\n              >\n                View My Code\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADA;AAFA;;;;AAsBA,kCAAkC;AAClC,MAAM,UAAuC;IAC3C,oBAAM,6LAAC,qMAAA,CAAA,OAAI;QAAC,WAAU;;;;;;IACtB,sBAAQ,6LAAC,yMAAA,CAAA,SAAM;QAAC,WAAU;;;;;;IAC1B,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IAC9B,sBAAQ,6LAAC,wNAAA,CAAA,SAAM;QAAC,WAAU;;;;;;IAC1B,mBAAK,6LAAC,mMAAA,CAAA,MAAG;QAAC,WAAU;;;;;;IACpB,yBAAW,6LAAC,+MAAA,CAAA,YAAS;QAAC,WAAU;;;;;;IAChC,0BAAY,6LAAC,iNAAA,CAAA,aAAU;QAAC,WAAU;;;;;;IAClC,4BAAc,6LAAC,mNAAA,CAAA,YAAS;QAAC,WAAU;;;;;;IACnC,8BAAgB,6LAAC,8NAAA,CAAA,cAAW;QAAC,WAAU;;;;;;IACvC,2BAAa,6LAAC,iNAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IACjC,sBAAQ,6LAAC,yMAAA,CAAA,SAAM;QAAC,WAAU;;;;;;IAC1B,qBAAO,6LAAC,uMAAA,CAAA,QAAK;QAAC,WAAU;;;;;;IACxB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;QAAC,WAAU;;;;;;IACxB,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IAC9B,2BAAa,6LAAC,iNAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;AACnC;AAEA,qBAAqB;AACrB,MAAM,SAAS;IACb;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;QACP,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,YAAY;QAChB,QAAQ;YAAE,SAAS;QAAE;QACrB,MAAM;YACJ,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,OAAO;QACX,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG;QAAE;IAC3B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;wBACX;sCACU,6LAAC;4BAAK,WAAU;sCAAqC;;;;;;;;;;;;8BAGhE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BACX;;;;;;8BAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,WAAU;8BAET,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,GAAG,MAAM,KAAK,CAAC,6DAA6D,CAAC;0DAC1F,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,GAAG,kBAAI,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAExD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EACb,MAAM,IAAI;;;;;;0EAEb,6LAAC;gEAAK,WAAU;0EACb,MAAM,QAAQ;;;;;;;;;;;;kEAGnB,6LAAC;wDAAG,WAAU;kEACX,MAAM,KAAK;;;;;;kEAEd,6LAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ;;;;;;kEAEjB,6LAAC;wDAAE,WAAU;kEACV,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;8CAO1B,6LAAC;oCAAI,WAAU;;;;;;;2BAhCV,MAAM,EAAE;;;;;;;;;;8BAsCnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAtHwB"}}, {"offset": {"line": 4014, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4020, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,OAAO,oKAAc,IAAI;AAE/B,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,oKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 4080, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4086, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/blog-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport Image from \"next/image\"\nimport { Calendar, Clock, ArrowRight, FileText, ExternalLink, BookOpen, Newspaper } from \"lucide-react\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from \"@/components/ui/tabs\"\n\n// External blog posts data (Medium articles)\nconst externalBlogPosts = [\n  {\n    id: 1,\n    title: \"Lessons Learned: Navigating the Transition from Student to Mobile Developer—Part 2\",\n    excerpt:\n      \"Continuing the journey of transitioning from student to professional mobile developer with more insights and practical advice.\",\n    image_url: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-pp1q3fydhMEtKSj59XryS7cbodunc1.png\",\n    category: \"Career Development\",\n    date: \"November 21, 2024\",\n    read_time: \"5 min read\",\n    slug: \"lessons-learned-part-2\",\n    url: \"https://medium.com/@yessinejawa/lessons-learned-navigating-the-transition-from-student-to-mobile-developer-part-2\",\n    type: \"external\"\n  },\n  {\n    id: 2,\n    title: \"Lessons Learned: Navigating the Transition from Student to Mobile Developer ⚡\",\n    excerpt:\n      \"Sharing my personal journey and key insights from transitioning from a student to a professional mobile developer.\",\n    image_url: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-pp1q3fydhMEtKSj59XryS7cbodunc1.png\",\n    category: \"Career Development\",\n    date: \"April 9, 2024\",\n    read_time: \"6 min read\",\n    slug: \"lessons-learned-part-1\",\n    url: \"https://medium.com/@yessinejawa/lessons-learned-navigating-the-transition-from-student-to-mobile-developer\",\n    type: \"external\"\n  },\n]\n\n// Local HTML articles data (will be populated when you add HTML files)\nconst localArticles = [\n  // Sample article to demonstrate functionality\n  {\n    id: 1,\n    title: \"Building Scalable React Native Apps\",\n    excerpt: \"Learn the key principles and best practices for creating maintainable and performant React Native applications that can scale with your business needs.\",\n    category: \"Technology\",\n    date: \"January 15, 2025\",\n    read_time: \"8 min read\",\n    htmlFile: \"sample-article.html\", // filename in public folder\n    tags: [\"React Native\", \"Architecture\", \"Performance\", \"Best Practices\"]\n  }\n  // Add your actual articles here following the same structure\n]\n\n// Function to escape apostrophes in text\nconst escapeApostrophes = (text: string): string => {\n  return text.replace(/'/g, \"&apos;\")\n}\n\n// Component for rendering external blog posts\nconst ExternalBlogCard = ({ post }: { post: any }) => (\n  <motion.div\n    initial={{ opacity: 0, y: 20 }}\n    animate={{ opacity: 1, y: 0 }}\n    className=\"group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300\"\n  >\n    <div className=\"flex flex-col md:flex-row\">\n      <div className=\"md:w-1/3 relative\">\n        <div className=\"aspect-video md:aspect-square relative\">\n          <Image\n            src={post.image_url || \"https://images.pexels.com/photos/839443/pexels-photo-839443.jpeg?auto=compress&cs=tinysrgb&w=600\"}\n            alt={post.title}\n            fill\n            className=\"object-cover transition-transform duration-500 group-hover:scale-105\"\n          />\n        </div>\n      </div>\n\n      <div className=\"md:w-2/3 p-6 md:p-8\">\n        <div className=\"flex items-center gap-2 mb-4\">\n          <Badge className=\"bg-rose-100 dark:bg-rose-900 text-rose-800 dark:text-rose-200\">\n            {post.category}\n          </Badge>\n          <Badge variant=\"outline\" className=\"text-xs\">\n            <ExternalLink className=\"h-3 w-3 mr-1\" />\n            Medium\n          </Badge>\n        </div>\n\n        <h3 className=\"text-xl md:text-2xl font-semibold mb-3 group-hover:text-rose-600 dark:group-hover:text-rose-400 transition-colors\">\n          {escapeApostrophes(post.title)}\n        </h3>\n\n        <p className=\"text-muted-foreground mb-4\">{escapeApostrophes(post.excerpt)}</p>\n\n        <div className=\"flex items-center text-sm text-muted-foreground mb-4\">\n          <Calendar className=\"h-4 w-4 mr-1\" />\n          <span className=\"mr-4\">{post.date}</span>\n          <Clock className=\"h-4 w-4 mr-1\" />\n          <span>{post.read_time}</span>\n        </div>\n\n        <Button\n          variant=\"link\"\n          className=\"p-0 h-auto text-rose-600 dark:text-rose-400 group-hover:underline\"\n          onClick={() => window.open(post.url, \"_blank\")}\n        >\n          Read More\n          <ArrowRight className=\"ml-1 h-4 w-4\" />\n        </Button>\n      </div>\n    </div>\n  </motion.div>\n)\n\n// Component for rendering local HTML articles\nconst LocalArticleCard = ({ article }: { article: any }) => (\n  <motion.div\n    initial={{ opacity: 0, y: 20 }}\n    animate={{ opacity: 1, y: 0 }}\n    className=\"group bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700\"\n  >\n    <div className=\"flex items-start justify-between mb-4\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"p-2 bg-rose-100 dark:bg-rose-900 rounded-lg\">\n          <FileText className=\"h-5 w-5 text-rose-600 dark:text-rose-400\" />\n        </div>\n        <Badge className=\"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\">\n          {article.category}\n        </Badge>\n      </div>\n      <Badge variant=\"outline\" className=\"text-xs\">\n        <BookOpen className=\"h-3 w-3 mr-1\" />\n        Original\n      </Badge>\n    </div>\n\n    <h3 className=\"text-xl font-semibold mb-3 group-hover:text-rose-600 dark:group-hover:text-rose-400 transition-colors\">\n      {article.title}\n    </h3>\n\n    <p className=\"text-muted-foreground mb-4 line-clamp-3\">{article.excerpt}</p>\n\n    {article.tags && (\n      <div className=\"flex flex-wrap gap-2 mb-4\">\n        {article.tags.map((tag: string, index: number) => (\n          <span\n            key={index}\n            className=\"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full\"\n          >\n            {tag}\n          </span>\n        ))}\n      </div>\n    )}\n\n    <div className=\"flex items-center justify-between\">\n      <div className=\"flex items-center text-sm text-muted-foreground\">\n        <Calendar className=\"h-4 w-4 mr-1\" />\n        <span className=\"mr-4\">{article.date}</span>\n        <Clock className=\"h-4 w-4 mr-1\" />\n        <span>{article.read_time}</span>\n      </div>\n\n      <Button\n        variant=\"link\"\n        className=\"p-0 h-auto text-rose-600 dark:text-rose-400 group-hover:underline\"\n        onClick={() => window.open(`/${article.htmlFile}`, \"_blank\")}\n      >\n        Read Article\n        <ArrowRight className=\"ml-1 h-4 w-4\" />\n      </Button>\n    </div>\n  </motion.div>\n)\n\nexport default function BlogSection() {\n  const [activeTab, setActiveTab] = useState(\"published\")\n\n  const container = {\n    hidden: { opacity: 0 },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  }\n\n  return (\n    <div className=\"min-h-screen p-8 md:p-12 bg-gradient-to-br from-rose-50 to-pink-100 dark:from-gray-900 dark:to-rose-950\">\n      <div className=\"max-w-6xl mx-auto\">\n        <motion.h2\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"text-4xl md:text-5xl font-bold mb-6 text-center lg:text-left\"\n        >\n          My <span className=\"text-rose-600 dark:text-rose-400\">Blog</span>\n        </motion.h2>\n\n        <motion.p\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.1 }}\n          className=\"text-lg text-muted-foreground mb-8 text-center lg:text-left\"\n        >\n          Thoughts, tutorials, and insights on mobile development and career growth\n        </motion.p>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n          className=\"mb-8\"\n        >\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n            <TabsList className=\"grid w-full grid-cols-2 lg:w-fit lg:grid-cols-2 mb-8\">\n              <TabsTrigger value=\"published\" className=\"flex items-center gap-2\">\n                <Newspaper className=\"h-4 w-4\" />\n                Published Articles\n              </TabsTrigger>\n              <TabsTrigger value=\"original\" className=\"flex items-center gap-2\">\n                <BookOpen className=\"h-4 w-4\" />\n                Original Content\n              </TabsTrigger>\n            </TabsList>\n\n            <AnimatePresence mode=\"wait\">\n              <TabsContent value=\"published\" className=\"mt-0\">\n                <motion.div\n                  key=\"published\"\n                  variants={container}\n                  initial=\"hidden\"\n                  animate=\"show\"\n                  exit=\"hidden\"\n                  className=\"space-y-8\"\n                >\n                  {externalBlogPosts.map((post) => (\n                    <ExternalBlogCard key={post.id} post={post} />\n                  ))}\n                </motion.div>\n\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 0.6 }}\n                  className=\"mt-12 text-center\"\n                >\n                  <Button\n                    variant=\"outline\"\n                    size=\"lg\"\n                    className=\"rounded-full group\"\n                    onClick={() => window.open(\"https://medium.com/@yessinejawa\", \"_blank\")}\n                  >\n                    View All on Medium\n                    <ExternalLink className=\"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\" />\n                  </Button>\n                </motion.div>\n              </TabsContent>\n\n              <TabsContent value=\"original\" className=\"mt-0\">\n                <motion.div\n                  key=\"original\"\n                  variants={container}\n                  initial=\"hidden\"\n                  animate=\"show\"\n                  exit=\"hidden\"\n                >\n                  {localArticles.length > 0 ? (\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      {localArticles.map((article) => (\n                        <LocalArticleCard key={article.id} article={article} />\n                      ))}\n                    </div>\n                  ) : (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className=\"text-center py-16\"\n                    >\n                      <div className=\"max-w-md mx-auto\">\n                        <div className=\"p-4 bg-gray-100 dark:bg-gray-800 rounded-full w-fit mx-auto mb-6\">\n                          <FileText className=\"h-12 w-12 text-gray-400\" />\n                        </div>\n                        <h3 className=\"text-xl font-semibold mb-3 text-gray-700 dark:text-gray-300\">\n                          Original Content Coming Soon\n                        </h3>\n                        <p className=\"text-muted-foreground mb-6\">\n                          This section will showcase exclusive articles and tutorials written specifically for this portfolio.\n                          Stay tuned for in-depth technical content and insights!\n                        </p>\n                        <div className=\"flex flex-wrap gap-2 justify-center\">\n                          <Badge variant=\"outline\">React Native</Badge>\n                          <Badge variant=\"outline\">Mobile Development</Badge>\n                          <Badge variant=\"outline\">TypeScript</Badge>\n                          <Badge variant=\"outline\">Career Tips</Badge>\n                        </div>\n                      </div>\n                    </motion.div>\n                  )}\n                </motion.div>\n              </TabsContent>\n            </AnimatePresence>\n          </Tabs>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AACA;AACA;AALA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAHA;;;;;;;;AAUA,6CAA6C;AAC7C,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,SACE;QACF,WAAW;QACX,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,SACE;QACF,WAAW;QACX,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,KAAK;QACL,MAAM;IACR;CACD;AAED,uEAAuE;AACvE,MAAM,gBAAgB;IACpB,8CAA8C;IAC9C;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;QACN,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAgB;YAAgB;YAAe;SAAiB;IACzE;CAED;AAED,yCAAyC;AACzC,MAAM,oBAAoB,CAAC;IACzB,OAAO,KAAK,OAAO,CAAC,MAAM;AAC5B;AAEA,8CAA8C;AAC9C,MAAM,mBAAmB,CAAC,EAAE,IAAI,EAAiB,iBAC/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,SAAS,IAAI;4BACvB,KAAK,KAAK,KAAK;4BACf,IAAI;4BACJ,WAAU;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6HAAA,CAAA,QAAK;oCAAC,WAAU;8CACd,KAAK,QAAQ;;;;;;8CAEhB,6LAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;sDACjC,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAK7C,6LAAC;4BAAG,WAAU;sCACX,kBAAkB,KAAK,KAAK;;;;;;sCAG/B,6LAAC;4BAAE,WAAU;sCAA8B,kBAAkB,KAAK,OAAO;;;;;;sCAEzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAQ,KAAK,IAAI;;;;;;8CACjC,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;8CAAM,KAAK,SAAS;;;;;;;;;;;;sCAGvB,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,GAAG,EAAE;;gCACtC;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAhD1B;AAuDN,8CAA8C;AAC9C,MAAM,mBAAmB,CAAC,EAAE,OAAO,EAAoB,iBACrD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;0CACd,QAAQ,QAAQ;;;;;;;;;;;;kCAGrB,6LAAC,6HAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAU,WAAU;;0CACjC,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKzC,6LAAC;gBAAG,WAAU;0BACX,QAAQ,KAAK;;;;;;0BAGhB,6LAAC;gBAAE,WAAU;0BAA2C,QAAQ,OAAO;;;;;;YAEtE,QAAQ,IAAI,kBACX,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAa,sBAC9B,6LAAC;wBAEC,WAAU;kCAET;uBAHI;;;;;;;;;;0BASb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAK,WAAU;0CAAQ,QAAQ,IAAI;;;;;;0CACpC,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;0CAAM,QAAQ,SAAS;;;;;;;;;;;;kCAG1B,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,QAAQ,EAAE,EAAE;;4BACpD;0CAEC,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;MAtDxB;AA4DS,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,YAAY;QAChB,QAAQ;YAAE,SAAS;QAAE;QACrB,MAAM;YACJ,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;wBACX;sCACI,6LAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;8BAGxD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BACX;;;;;;8BAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC,4HAAA,CAAA,OAAI;wBAAC,OAAO;wBAAW,eAAe;wBAAc,WAAU;;0CAC7D,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,6LAAC,+MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGnC,6LAAC,4HAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;;0DACtC,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAKpC,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;;kDACpB,6LAAC,4HAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,UAAU;gDACV,SAAQ;gDACR,SAAQ;gDACR,MAAK;gDACL,WAAU;0DAET,kBAAkB,GAAG,CAAC,CAAC,qBACtB,6LAAC;wDAA+B,MAAM;uDAAf,KAAK,EAAE;;;;;+CAR5B;;;;;0DAYN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;0DAEV,cAAA,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,OAAO,IAAI,CAAC,mCAAmC;;wDAC/D;sEAEC,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAK9B,6LAAC,4HAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;kDACtC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,UAAU;4CACV,SAAQ;4CACR,SAAQ;4CACR,MAAK;sDAEJ,cAAc,MAAM,GAAG,kBACtB,6LAAC;gDAAI,WAAU;0DACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,6LAAC;wDAAkC,SAAS;uDAArB,QAAQ,EAAE;;;;;;;;;qEAIrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC;4DAAG,WAAU;sEAA8D;;;;;;sEAG5E,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAI1C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAU;;;;;;8EACzB,6LAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAU;;;;;;8EACzB,6LAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAU;;;;;;8EACzB,6LAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAU;;;;;;;;;;;;;;;;;;;;;;;2CAjC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CtB;GArIwB;MAAA"}}, {"offset": {"line": 4871, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4877, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 4907, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4913, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Textarea = React.forwardRef<\n  HTMLTextAreaElement,\n  React.ComponentProps<\"textarea\">\n>(({ className, ...props }, ref) => {\n  return (\n    <textarea\n      className={cn(\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  )\n})\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AACA,SAAS,WAAW,GAAG"}}, {"offset": {"line": 4942, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4948, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/contact-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { motion } from \"framer-motion\"\nimport { useForm } from \"react-hook-form\"\nimport { zodResolver } from \"@hookform/resolvers/zod\"\nimport * as z from \"zod\"\nimport emailjs from '@emailjs/browser'\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Loader2, Send, Mail, Phone, Code, Users, MessageCircle } from \"lucide-react\"\n\n// Static profile data\nconst profile = {\n  name: \"Yessine JAOUA\",\n  email: \"<EMAIL>\",\n  phone: \"+212 620 72 26 02\",\n  location: \"Your Location\",\n}\n\n// Static social links\nconst socialLinks = [\n  { id: 1, name: \"GitHub\", url: \"https://github.com/apachi1444\", icon: <Code className=\"h-6 w-6\" /> },\n  { id: 2, name: \"LinkedIn\", url: \"https://linkedin.com/in/yessine-jaoua\", icon: <Users className=\"h-6 w-6\" /> },\n  { id: 3, name: \"Twitter\", url: \"https://twitter.com/JawaYessine\", icon: <MessageCircle className=\"h-6 w-6\" /> },\n]\n\nconst formSchema = z.object({\n  email: z.string().email(\"Please enter a valid email address\"),\n  message: z.string().min(10, \"Message must be at least 10 characters\"),\n})\n\ntype FormData = z.infer<typeof formSchema>\n\nexport default function ContactSection() {\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [submitSuccess, setSubmitSuccess] = useState(false)\n  const [submitError, setSubmitError] = useState(false)\n\n  const {\n    register,\n    handleSubmit,\n    reset,\n    formState: { errors },\n  } = useForm<FormData>({\n    resolver: zodResolver(formSchema),\n  })\n\n  const onSubmit = async (data: FormData) => {\n    setIsSubmitting(true)\n    setSubmitSuccess(false)\n    setSubmitError(false)\n\n    try {\n      // EmailJS configuration from environment variables\n      const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID\n      const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID\n      const publicKey = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY\n\n      // Check if all required environment variables are set\n      if (!serviceId || !templateId || !publicKey) {\n        // Fallback for development - just log the data\n        console.log('EmailJS not configured. Form data:', data)\n        console.log('To set up EmailJS, see EMAILJS_SETUP.md')\n\n        // Simulate delay for better UX\n        await new Promise((resolve) => setTimeout(resolve, 1000))\n      } else {\n        // Template parameters for EmailJS\n        const templateParams = {\n          from_email: data.email,\n          message: data.message,\n          to_email: profile.email, // Your email where you want to receive messages\n        }\n\n        // Send email using EmailJS\n        await emailjs.send(serviceId, templateId, templateParams, publicKey)\n      }\n\n      // Success\n      setSubmitSuccess(true)\n      reset()\n\n      // Reset success message after 5 seconds\n      setTimeout(() => setSubmitSuccess(false), 5000)\n    } catch (err) {\n      console.error(\"Error submitting form:\", err)\n      setSubmitError(true)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const contactInfo = [\n    {\n      icon: <Mail className=\"h-6 w-6 text-blue-500\" />,\n      title: \"Email\",\n      value: profile.email,\n      link: `mailto:${profile.email}`,\n      description: \"Feel free to email me anytime\",\n    },\n    {\n      icon: <Phone className=\"h-6 w-6 text-green-500\" />,\n      title: \"Phone\",\n      value: profile.phone,\n      link: `tel:${profile.phone}`,\n      description: \"Available for calls during business hours.\",\n    },\n  ]\n\n  return (\n    <div className=\"min-h-screen p-8 md:p-12 bg-gradient-to-br from-blue-50 to-cyan-100 dark:from-gray-900 dark:to-blue-950\">\n      <div className=\"max-w-5xl mx-auto\">\n        <motion.h2\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"text-4xl md:text-5xl font-bold mb-6 text-center lg:text-left\"\n        >\n          Get in <span className=\"text-blue-600 dark:text-blue-400\">Touch</span>\n        </motion.h2>\n\n        <motion.p\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.1 }}\n          className=\"text-lg text-muted-foreground mb-12 text-center lg:text-left\"\n        >\n          Have a project in mind or want to chat? I&apos;d love to hear from you!\n        </motion.p>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 md:p-8 rounded-xl shadow-lg\">\n              <h3 className=\"text-2xl font-semibold mb-6\">Send a Message</h3>\n\n              <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium mb-2\">\n                    Email\n                  </label>\n                  <Input\n                    id=\"email\"\n                    type=\"email\"\n                    placeholder=\"<EMAIL>\"\n                    {...register(\"email\")}\n                    className={errors.email ? \"border-destructive\" : \"\"}\n                  />\n                  {errors.email && <p className=\"mt-1 text-sm text-destructive\">{errors.email.message}</p>}\n                </div>\n\n                <div>\n                  <label htmlFor=\"message\" className=\"block text-sm font-medium mb-2\">\n                    Message\n                  </label>\n                  <Textarea\n                    id=\"message\"\n                    placeholder=\"Your message\"\n                    rows={8}\n                    {...register(\"message\")}\n                    className={errors.message ? \"border-destructive\" : \"\"}\n                  />\n                  {errors.message && <p className=\"mt-1 text-sm text-destructive\">{errors.message.message}</p>}\n                </div>\n\n                <Button type=\"submit\" disabled={isSubmitting} className=\"w-full rounded-full\">\n                  {isSubmitting ? (\n                    <>\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                      Sending...\n                    </>\n                  ) : (\n                    <>\n                      <Send className=\"mr-2 h-4 w-4\" />\n                      Send Message\n                    </>\n                  )}\n                </Button>\n\n                {submitSuccess && (\n                  <div className=\"p-4 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-md\">\n                    ✅ Your message has been sent successfully! I&apos;ll get back to you soon.\n                  </div>\n                )}\n\n                {submitError && (\n                  <div className=\"p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-md\">\n                    ❌ There was an error sending your message. Please try again or contact me directly via email.\n                  </div>\n                )}\n              </form>\n            </div>\n          </motion.div>\n\n          {/* Contact Information */}\n          <motion.div\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.5, delay: 0.3 }}\n            className=\"space-y-8\"\n          >\n            <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 md:p-8 rounded-xl shadow-lg\">\n              <h3 className=\"text-2xl font-semibold mb-6\">Contact Information</h3>\n\n              <div className=\"space-y-8\">\n                {contactInfo.map((info, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}\n                    className=\"flex items-start\"\n                  >\n                    <div className=\"p-3 rounded-full bg-blue-100 dark:bg-blue-900/50 mr-4\">{info.icon}</div>\n                    <div>\n                      <h4 className=\"font-medium text-lg\">{info.title}</h4>\n                      {info.link ? (\n                        <a href={info.link} className=\"text-blue-600 dark:text-blue-400 hover:underline font-medium\">\n                          {info.value}\n                        </a>\n                      ) : (\n                        <p className=\"font-medium\">{info.value}</p>\n                      )}\n                      <p className=\"text-muted-foreground mt-1\">{info.description}</p>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n\n              <div className=\"mt-8 pt-6 border-t border-gray-200 dark:border-gray-700\">\n                <h4 className=\"font-medium text-lg mb-4\">Connect With Me</h4>\n                <div className=\"flex flex-wrap gap-4\">\n                  {socialLinks.map((link) => (\n                    <a\n                      key={link.id}\n                      href={link.url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"p-3 rounded-full bg-blue-100 dark:bg-blue-900/50 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors\"\n                      aria-label={link.name}\n                    >\n                      {link.icon}\n                    </a>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AALA;AAFA;AAoDwB;AAjDxB;AAIA;AAAA;AARA;AAQA;AAAA;;;AAXA;;;;;;;;;;;AAaA,sBAAsB;AACtB,MAAM,UAAU;IACd,MAAM;IACN,OAAO;IACP,OAAO;IACP,UAAU;AACZ;AAEA,sBAAsB;AACtB,MAAM,cAAc;IAClB;QAAE,IAAI;QAAG,MAAM;QAAU,KAAK;QAAiC,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAAa;IAClG;QAAE,IAAI;QAAG,MAAM;QAAY,KAAK;QAAyC,oBAAM,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;IAAa;IAC7G;QAAE,IAAI;QAAG,MAAM;QAAW,KAAK;QAAmC,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;IAAa;CAC/G;AAED,MAAM,aAAa,wIAAE,MAAM,CAAC;IAC1B,OAAO,wIAAE,MAAM,GAAG,KAAK,CAAC;IACxB,SAAS,wIAAE,MAAM,GAAG,GAAG,CAAC,IAAI;AAC9B;AAIe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAY;QACpB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAChB,iBAAiB;QACjB,eAAe;QAEf,IAAI;YACF,mDAAmD;YACnD,MAAM,YAAY,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,8BAA8B;YAC5D,MAAM,aAAa,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,+BAA+B;YAC9D,MAAM,YAAY,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,8BAA8B;YAE5D,sDAAsD;YACtD,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW;gBAC3C,+CAA+C;gBAC/C,QAAQ,GAAG,CAAC,sCAAsC;gBAClD,QAAQ,GAAG,CAAC;gBAEZ,+BAA+B;gBAC/B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YACrD,OAAO;gBACL,kCAAkC;gBAClC,MAAM,iBAAiB;oBACrB,YAAY,KAAK,KAAK;oBACtB,SAAS,KAAK,OAAO;oBACrB,UAAU,QAAQ,KAAK;gBACzB;gBAEA,2BAA2B;gBAC3B,MAAM,sKAAA,CAAA,UAAO,CAAC,IAAI,CAAC,WAAW,YAAY,gBAAgB;YAC5D;YAEA,UAAU;YACV,iBAAiB;YACjB;YAEA,wCAAwC;YACxC,WAAW,IAAM,iBAAiB,QAAQ;QAC5C,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,eAAe;QACjB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB;YACE,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,OAAO,QAAQ,KAAK;YACpB,MAAM,CAAC,OAAO,EAAE,QAAQ,KAAK,EAAE;YAC/B,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,OAAO,QAAQ,KAAK;YACpB,MAAM,CAAC,IAAI,EAAE,QAAQ,KAAK,EAAE;YAC5B,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;wBACX;sCACQ,6LAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;8BAG5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BACX;;;;;;8BAID,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAE5C,6LAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAChD,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAAiC;;;;;;kEAGlE,6LAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,aAAY;wDACX,GAAG,SAAS,QAAQ;wDACrB,WAAW,OAAO,KAAK,GAAG,uBAAuB;;;;;;oDAElD,OAAO,KAAK,kBAAI,6LAAC;wDAAE,WAAU;kEAAiC,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0DAGrF,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAiC;;;;;;kEAGpE,6LAAC,gIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,aAAY;wDACZ,MAAM;wDACL,GAAG,SAAS,UAAU;wDACvB,WAAW,OAAO,OAAO,GAAG,uBAAuB;;;;;;oDAEpD,OAAO,OAAO,kBAAI,6LAAC;wDAAE,WAAU;kEAAiC,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAGzF,6LAAC,8HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,UAAU;gDAAc,WAAU;0DACrD,6BACC;;sEACE,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAA8B;;iFAInD;;sEACE,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;4CAMtC,+BACC,6LAAC;gDAAI,WAAU;0DAAmF;;;;;;4CAKnG,6BACC,6LAAC;gDAAI,WAAU;0DAA2E;;;;;;;;;;;;;;;;;;;;;;;sCASlG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAE5C,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO,MAAM,QAAQ;gDAAI;gDACtD,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEAAyD,KAAK,IAAI;;;;;;kEACjF,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAuB,KAAK,KAAK;;;;;;4DAC9C,KAAK,IAAI,iBACR,6LAAC;gEAAE,MAAM,KAAK,IAAI;gEAAE,WAAU;0EAC3B,KAAK,KAAK;;;;;qFAGb,6LAAC;gEAAE,WAAU;0EAAe,KAAK,KAAK;;;;;;0EAExC,6LAAC;gEAAE,WAAU;0EAA8B,KAAK,WAAW;;;;;;;;;;;;;+CAhBxD;;;;;;;;;;kDAsBX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2B;;;;;;0DACzC,6LAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;wDAEC,MAAM,KAAK,GAAG;wDACd,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,cAAY,KAAK,IAAI;kEAEpB,KAAK,IAAI;uDAPL,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBlC;GA/NwB;;QAUlB,iKAAA,CAAA,UAAO;;;KAVW"}}, {"offset": {"line": 5506, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5512, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,eAAe,gLAAsB,IAAI;AAE/C,MAAM,sBAAsB,gLAAsB,OAAO;AAEzD,MAAM,oBAAoB,gLAAsB,KAAK;AAErD,MAAM,qBAAqB,gLAAsB,MAAM;AAEvD,MAAM,kBAAkB,gLAAsB,GAAG;AAEjD,MAAM,yBAAyB,gLAAsB,UAAU;AAE/D,MAAM,uCAAyB,8JAAM,UAAU,MAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,gLAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,gLAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,uCAAyB,8JAAM,UAAU,OAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,gLAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,gLAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,oCAAsB,8JAAM,UAAU,OAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,gLAAsB,MAAM;kBAC3B,cAAA,6LAAC,gLAAsB,OAAO;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sLACA,oVACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,gLAAsB,OAAO,CAAC,WAAW;AAE3E,MAAM,iCAAmB,8JAAM,UAAU,OAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,gLAAsB,IAAI;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yQACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,gLAAsB,IAAI,CAAC,WAAW;AAErE,MAAM,yCAA2B,8JAAM,UAAU,OAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC,gLAAsB,YAAY;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gLAAsB,aAAa;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,gLAAsB,YAAY,CAAC,WAAW;AAEhD,MAAM,sCAAwB,8JAAM,UAAU,QAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,gLAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gLAAsB,aAAa;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,gLAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,kCAAoB,8JAAM,UAAU,QAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,gLAAsB,KAAK;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,gLAAsB,KAAK,CAAC,WAAW;AAEvE,MAAM,sCAAwB,8JAAM,UAAU,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,gLAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,gLAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG"}}, {"offset": {"line": 5734, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5740, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/mode-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\nimport { Button } from \"@/components/ui/button\"\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\"\n\nexport function ModeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"outline\"\n          size=\"icon\"\n          className=\"rounded-full bg-background/50 backdrop-blur-sm border-white/20\"\n        >\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>Light</DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>Dark</DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>System</DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAHA;AAAA;;;AAFA;;;;;AAOO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,6LAAC,wIAAA,CAAA,eAAY;;0BACX,6LAAC,wIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,wIAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,6LAAC,wIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCACpD,6LAAC,wIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCACnD,6LAAC,wIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAI7D;GAvBgB;;QACO,mJAAA,CAAA,WAAQ;;;KADf"}}, {"offset": {"line": 5852, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5858, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/my-portfolio/my-app/components/split-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useCallback } from \"react\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport Sidebar from \"./sidebar\"\nimport HomeSection from \"@/components/home-section\"\nimport AboutSection from \"@/components/about-section\"\nimport PortfolioSection from \"@/components/portfolio-section\"\nimport AwardsSection from \"@/components/awards-section\"\nimport BlogSection from \"@/components/blog-section\"\nimport ContactSection from \"@/components/contact-section\"\nimport { ModeToggle } from \"./mode-toggle\"\n\n// Define all available sections\nexport type SectionType = \"home\" | \"about\" | \"portfolio\" | \"awards\" | \"blog\" | \"contact\" | \"admin\"\n\nexport default function SplitLayout() {\n  const [mounted, setMounted] = useState(false)\n  const [activeSection, setActiveSection] = useState<SectionType>(\"home\")\n  const [isScrolling, setIsScrolling] = useState(false)\n  const [isAdmin, setIsAdmin] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Secret admin mode with Ctrl+Shift+A\n  const handleKeyDown = useCallback((e: KeyboardEvent) => {\n    if (e.ctrlKey && e.shiftKey && e.key === \"A\") {\n      setIsAdmin(true)\n      setActiveSection(\"admin\")\n    }\n  }, [])\n\n  useEffect(() => {\n    window.addEventListener(\"keydown\", handleKeyDown)\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\n  }, [handleKeyDown])\n\n  // Listen for custom navigation events\n  useEffect(() => {\n    const handleNavigateToSection = (event: CustomEvent) => {\n      const { section } = event.detail\n      if ([\"home\", \"about\", \"portfolio\", \"awards\", \"blog\", \"contact\"].includes(section)) {\n        setActiveSection(section as SectionType)\n      }\n    }\n\n    window.addEventListener('navigate-to-section', handleNavigateToSection as EventListener)\n    return () => window.removeEventListener('navigate-to-section', handleNavigateToSection as EventListener)\n  }, [])\n\n  // Handle section change from sidebar\n  const handleSectionChange = (section: SectionType) => {\n    setActiveSection(section)\n\n    // Update URL without page reload\n    window.history.pushState({}, \"\", `#${section}`)\n\n    // Scroll to section for mobile view\n    const element = document.getElementById(section)\n    if (element && window.innerWidth < 1024) {\n      setIsScrolling(true)\n      element.scrollIntoView({ behavior: \"smooth\" })\n      setTimeout(() => setIsScrolling(false), 1000)\n    }\n  }\n\n  // Handle scroll for mobile view\n  useEffect(() => {\n    const handleScroll = () => {\n      if (isScrolling || window.innerWidth >= 1024) return\n\n      const sections = [\"home\", \"about\", \"portfolio\", \"awards\", \"blog\", \"contact\"]\n\n      for (const section of sections) {\n        const element = document.getElementById(section)\n        if (!element) continue\n\n        const rect = element.getBoundingClientRect()\n        if (rect.top <= 100 && rect.bottom >= 100) {\n          setActiveSection(section as SectionType)\n          window.history.replaceState({}, \"\", `#${section}`)\n          break\n        }\n      }\n    }\n\n    window.addEventListener(\"scroll\", handleScroll)\n    return () => window.removeEventListener(\"scroll\", handleScroll)\n  }, [isScrolling])\n\n  // Check URL hash on initial load\n  useEffect(() => {\n    const hash = window.location.hash.replace(\"#\", \"\")\n    if (hash && [\"home\", \"about\", \"portfolio\", \"awards\", \"blog\", \"contact\", \"admin\"].includes(hash)) {\n      setActiveSection(hash as SectionType)\n    }\n  }, [])\n\n  if (!mounted) {\n    return null // Prevent rendering until client-side\n  }\n\n  return (\n    <div className=\"flex flex-col lg:flex-row min-h-screen bg-background\">\n      {/* Fixed sidebar for desktop, top navigation for mobile */}\n      <Sidebar activeSection={activeSection} onSectionChange={handleSectionChange} showAdmin={isAdmin} />\n\n      {/* Theme toggle button */}\n      <div className=\"fixed top-20 right-4 lg:top-4 lg:right-4 z-40\">\n        <ModeToggle />\n      </div>\n\n      {/* Main content area */}\n      <main className=\"flex-1 lg:ml-[35vw]\">\n        {/* Desktop view with animated transitions */}\n        <div className=\"hidden lg:block h-screen overflow-hidden\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={activeSection}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3 }}\n              className=\"h-full overflow-y-auto scrollbar-thin\"\n            >\n              {renderSection(activeSection)}\n            </motion.div>\n          </AnimatePresence>\n        </div>\n\n        {/* Mobile view with all sections stacked */}\n        <div className=\"block lg:hidden\">\n          {/* Mobile view sections */}\n          <section id=\"home\" className=\"min-h-screen\">\n            <HomeSection />\n          </section>\n          <section id=\"about\" className=\"min-h-screen\">\n            <AboutSection />\n          </section>\n          <section id=\"portfolio\" className=\"min-h-screen\">\n            <PortfolioSection />\n          </section>\n          <section id=\"awards\" className=\"min-h-screen\">\n            <AwardsSection />\n          </section>\n          <section id=\"blog\" className=\"min-h-screen\">\n            <BlogSection />\n          </section>\n          <section id=\"contact\" className=\"min-h-screen\">\n            <ContactSection />\n          </section>\n        </div>\n      </main>\n    </div>\n  )\n}\n\n// Helper function to render the active section\nfunction renderSection(section: SectionType) {\n  switch (section) {\n    case \"home\":\n      return <HomeSection />\n    case \"about\":\n      return <AboutSection />\n    case \"portfolio\":\n      return <PortfolioSection />\n    case \"awards\":\n      return <AwardsSection />\n    case \"blog\":\n      return <BlogSection />\n    case \"contact\":\n      return <ContactSection />\n    default:\n      return <HomeSection />\n  }\n}\n\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AAAA;;;AAHA;;;;;;;;;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE;IAEL,sCAAsC;IACtC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YACjC,IAAI,EAAE,OAAO,IAAI,EAAE,QAAQ,IAAI,EAAE,GAAG,KAAK,KAAK;gBAC5C,WAAW;gBACX,iBAAiB;YACnB;QACF;iDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,OAAO,gBAAgB,CAAC,WAAW;YACnC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;gCAAG;QAAC;KAAc;IAElB,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;iEAA0B,CAAC;oBAC/B,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,MAAM;oBAChC,IAAI;wBAAC;wBAAQ;wBAAS;wBAAa;wBAAU;wBAAQ;qBAAU,CAAC,QAAQ,CAAC,UAAU;wBACjF,iBAAiB;oBACnB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C;yCAAO,IAAM,OAAO,mBAAmB,CAAC,uBAAuB;;QACjE;gCAAG,EAAE;IAEL,qCAAqC;IACrC,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB;QAEjB,iCAAiC;QACjC,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,SAAS;QAE9C,oCAAoC;QACpC,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,WAAW,OAAO,UAAU,GAAG,MAAM;YACvC,eAAe;YACf,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;YAC5C,WAAW,IAAM,eAAe,QAAQ;QAC1C;IACF;IAEA,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;sDAAe;oBACnB,IAAI,eAAe,OAAO,UAAU,IAAI,MAAM;oBAE9C,MAAM,WAAW;wBAAC;wBAAQ;wBAAS;wBAAa;wBAAU;wBAAQ;qBAAU;oBAE5E,KAAK,MAAM,WAAW,SAAU;wBAC9B,MAAM,UAAU,SAAS,cAAc,CAAC;wBACxC,IAAI,CAAC,SAAS;wBAEd,MAAM,OAAO,QAAQ,qBAAqB;wBAC1C,IAAI,KAAK,GAAG,IAAI,OAAO,KAAK,MAAM,IAAI,KAAK;4BACzC,iBAAiB;4BACjB,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,SAAS;4BACjD;wBACF;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;gCAAG;QAAC;KAAY;IAEhB,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,OAAO,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK;YAC/C,IAAI,QAAQ;gBAAC;gBAAQ;gBAAS;gBAAa;gBAAU;gBAAQ;gBAAW;aAAQ,CAAC,QAAQ,CAAC,OAAO;gBAC/F,iBAAiB;YACnB;QACF;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,OAAO,KAAK,sCAAsC;;IACpD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,yHAAA,CAAA,UAAO;gBAAC,eAAe;gBAAe,iBAAiB;gBAAqB,WAAW;;;;;;0BAGxF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,aAAU;;;;;;;;;;0BAIb,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;4BAAC,MAAK;sCACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAET,cAAc;+BAPV;;;;;;;;;;;;;;;kCAaX,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAQ,IAAG;gCAAO,WAAU;0CAC3B,cAAA,6LAAC,iIAAA,CAAA,UAAW;;;;;;;;;;0CAEd,6LAAC;gCAAQ,IAAG;gCAAQ,WAAU;0CAC5B,cAAA,6LAAC,kIAAA,CAAA,UAAY;;;;;;;;;;0CAEf,6LAAC;gCAAQ,IAAG;gCAAY,WAAU;0CAChC,cAAA,6LAAC,sIAAA,CAAA,UAAgB;;;;;;;;;;0CAEnB,6LAAC;gCAAQ,IAAG;gCAAS,WAAU;0CAC7B,cAAA,6LAAC,mIAAA,CAAA,UAAa;;;;;;;;;;0CAEhB,6LAAC;gCAAQ,IAAG;gCAAO,WAAU;0CAC3B,cAAA,6LAAC,iIAAA,CAAA,UAAW;;;;;;;;;;0CAEd,6LAAC;gCAAQ,IAAG;gCAAU,WAAU;0CAC9B,cAAA,6LAAC,oIAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;GA7IwB;KAAA;AA+IxB,+CAA+C;AAC/C,SAAS,cAAc,OAAoB;IACzC,OAAQ;QACN,KAAK;YACH,qBAAO,6LAAC,iIAAA,CAAA,UAAW;;;;;QACrB,KAAK;YACH,qBAAO,6LAAC,kIAAA,CAAA,UAAY;;;;;QACtB,KAAK;YACH,qBAAO,6LAAC,sIAAA,CAAA,UAAgB;;;;;QAC1B,KAAK;YACH,qBAAO,6LAAC,mIAAA,CAAA,UAAa;;;;;QACvB,KAAK;YACH,qBAAO,6LAAC,iIAAA,CAAA,UAAW;;;;;QACrB,KAAK;YACH,qBAAO,6LAAC,oIAAA,CAAA,UAAc;;;;;QACxB;YACE,qBAAO,6LAAC,iIAAA,CAAA,UAAW;;;;;IACvB;AACF"}}, {"offset": {"line": 6228, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}