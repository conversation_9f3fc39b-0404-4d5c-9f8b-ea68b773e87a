<!DOCTYPE html>
<html lang="english">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Master React Native blob images! Display, upload, performance tips, caching & conversions (base64, file). Solve common issues now!">
  <meta name="keywords" content="React native blob image">
  <meta name="author" content="AI Generated">
  <meta property="og:title" content="React Native Blob Image Display: Your Mastery Guide!">
  <meta property="og:description" content="Master React Native blob images! Display, upload, performance tips, caching & conversions (base64, file). Solve common issues now!">
  <meta property="og:image" content="">
  <meta property="og:url" content="react-native-blob-image">
  <meta property="og:type" content="article">
  <meta name="twitter:card" content="summary_large_image">
  <link rel="canonical" href="react-native-blob-image">
  <title>React Native Blob Image Display: Your Mastery Guide!</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<style>
        section a, article a, div a {
            color: #1a73e8;
            text-decoration: underline;
        }
  </style>
<body>
    
    <!---TITLE--->
    
        <div class="max-w-4xl mx-auto px-4 py-8 bg-white min-h-screen">
    <!-- Header Section -->
    <header class="mb-12">
      <div class="flex items-center text-sm text-purple-600 mb-3 font-medium">
      </div>
      <h1 class="text-4xl md:text-5xl font-bold mb-4 text-gray-900">
        React Native Blob Image Mastery: Display
      </h1>
        
    <!---END TITLE--->

    <!---READING LABEL--->
    
            <div class="flex items-center text-gray-600 text-sm">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-1">
          <circle cx="12" cy="12" r="10"></circle>
          <polyline points="12 6 12 12 16 14"></polyline>
        </svg>
        <span>Time required: 5 minutes</span>
      </div>
    </header>
    
        
    <!---END READING LABEL--->

    <!---TABLE OF CONTENTS--->
    
            <section class="mb-8 bg-purple-50 rounded-lg p-5">
              <div class="flex items-center mb-4 text-purple-800">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 mr-2">
                  <line x1="8" y1="6" x2="21" y2="6"></line>
                  <line x1="8" y1="12" x2="21" y2="12"></line>
                  <line x1="8" y1="18" x2="21" y2="18"></line>
                  <line x1="3" y1="6" x2="3.01" y2="6"></line>
                  <line x1="3" y1="12" x2="3.01" y2="12"></line>
                  <line x1="3" y1="18" x2="3.01" y2="18"></line>
                </svg>
                <h2 class="text-lg font-semibold">Table of Contents</h2>
              </div>
              <nav>
                <ul class="space-y-2">
            
                  <li class="font-medium list-none">
                    <a href="#introduction" class="text-purple-700 hover:text-purple-900 transition-colors">
                      Introduction
                    </a>
                  </li>
                  <li class="font-medium list-none">
                    <a href="#displaying-blob-images-in-react-native" class="text-purple-700 hover:text-purple-900 transition-colors">
                      Displaying Blob Images in React Native
                    </a>
                  </li>
                  <li class="list-disc list-inside text-purple-700">
                    <span>
                      <a href="#creating-a-blob-url-from-image-data" class="text-purple-700 hover:text-purple-900 transition-colors">
                        Creating a Blob URL from Image Data
                      </a>
                    </span>
                  </li>
                  <li class="list-disc list-inside text-purple-700">
                    <span>
                      <a href="#using-blob-urls-with-the-<image>-component" class="text-purple-700 hover:text-purple-900 transition-colors">
                        Using Blob URLs with the <Image> Component
                      </a>
                    </span>
                  </li>
                  <li class="list-disc list-inside text-purple-700">
                    <span>
                      <a href="#handling-different-image-formats-(jpeg,-png,-etc.)" class="text-purple-700 hover:text-purple-900 transition-colors">
                        Handling Different Image Formats (JPEG, PNG, etc.)
                      </a>
                    </span>
                  </li>
                  <li class="list-disc list-inside text-purple-700">
                    <span>
                      <a href="#troubleshooting-common-display-issues" class="text-purple-700 hover:text-purple-900 transition-colors">
                        Troubleshooting Common Display Issues
                      </a>
                    </span>
                  </li>
                  <li class="font-medium list-none">
                    <a href="#optimizing-react-native-blob-image-performance" class="text-purple-700 hover:text-purple-900 transition-colors">
                      Optimizing React Native Blob Image Performance
                    </a>
                  </li>
                  <li class="font-medium list-none">
                    <a href="#blob-image-conversion-and-handling" class="text-purple-700 hover:text-purple-900 transition-colors">
                      Blob Image Conversion and Handling
                    </a>
                  </li>
                  <li class="list-disc list-inside text-purple-700">
                    <span>
                      <a href="#converting-base64-to-blob-images" class="text-purple-700 hover:text-purple-900 transition-colors">
                        Converting Base64 to Blob Images
                      </a>
                    </span>
                  </li>
                  <li class="list-disc list-inside text-purple-700">
                    <span>
                      <a href="#converting-files-to-blob-images" class="text-purple-700 hover:text-purple-900 transition-colors">
                        Converting Files to Blob Images
                      </a>
                    </span>
                  </li>
                  <li class="list-disc list-inside text-purple-700">
                    <span>
                      <a href="#react-native-image-picker-and-blobs" class="text-purple-700 hover:text-purple-900 transition-colors">
                        React Native Image Picker and Blobs
                      </a>
                    </span>
                  </li>
                  <li class="list-disc list-inside text-purple-700">
                    <span>
                      <a href="#uploading-blob-images" class="text-purple-700 hover:text-purple-900 transition-colors">
                        Uploading Blob Images
                      </a>
                    </span>
                  </li>
                  <li class="font-medium list-none">
                    <a href="#advanced-blob-image-management" class="text-purple-700 hover:text-purple-900 transition-colors">
                      Advanced Blob Image Management
                    </a>
                  </li>
                  <li class="list-disc list-inside text-purple-700">
                    <span>
                      <a href="#blob-image-caching-strategies" class="text-purple-700 hover:text-purple-900 transition-colors">
                        Blob Image Caching Strategies
                      </a>
                    </span>
                  </li>
                  <li class="list-disc list-inside text-purple-700">
                    <span>
                      <a href="#implementing-a-blob-image-viewer" class="text-purple-700 hover:text-purple-900 transition-colors">
                        Implementing a Blob Image Viewer
                      </a>
                    </span>
                  </li>
                  <li class="list-disc list-inside text-purple-700">
                    <span>
                      <a href="#addressing-memory-management-concerns" class="text-purple-700 hover:text-purple-900 transition-colors">
                        Addressing Memory Management Concerns
                      </a>
                    </span>
                  </li>
                  <li class="font-medium list-none">
                    <a href="#conclusion" class="text-purple-700 hover:text-purple-900 transition-colors">
                      Conclusion
                    </a>
                  </li>
                </ul>
              </nav>
            </section>
            
    <!---END TABLE OF CONTENTS--->
    
    <!---MAGIC--->
    
            <!--Introduction -->
            <section id="Introduction">
<h2 class="text-2xl font-bold mb-4 text-gray-800">Introduction</h2>
<p class="mb-4 text-gray-700 leading-relaxed">Welcome to the wonderful world of displaying Blob images in React Native! If you've ever wrestled with getting images from unusual sources to show up correctly in your app, you're in the right place. This guide will walk you through the ins and outs of handling Blob data and transforming it into visually appealing UI elements.</p>
<p class="mb-4 text-gray-700 leading-relaxed">Blobs, or Binary Large Objects, often come into play when dealing with file uploads, camera access, or fetching images from certain APIs. Unlike simple image URLs, Blobs require a bit more finesse to render. But don't worry, it's not as intimidating as it sounds! We'll break down the process step-by-step, ensuring you understand each part of the puzzle.</p>
<p class="mb-4 text-gray-700 leading-relaxed">In the following sections, we'll cover everything from converting a Blob to a usable image source, to displaying that image using React Native's <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">&lt;Image&gt;</code> component. I'll also share some tips and tricks I've learned along the way, helping you avoid common pitfalls and optimize your image loading for a smooth user experience.</p>
</section>
            <!--Section one -->
            <section id="heading='Displaying Blob Images in React Native' subheadings=['Creating a Blob URL from Image Data', 'Using Blob URLs with the &lt;Image&gt; Component', 'Handling Different Image Formats (JPEG, PNG, etc.)', 'Troubleshooting Common Display Issues']">
<h2 class="text-2xl font-bold mb-4 text-gray-800">Displaying Blob Images in React Native</h2>
<p class="mb-4 text-gray-700 leading-relaxed">Okay, you've got your image data as a Blob – awesome! Now, let's get that image showing up in your React Native app. This section will walk you through the process, from creating a URL for your Blob to handling different image formats and troubleshooting any hiccups along the way.</p>
<h3 class="text-xl font-semibold mb-3 text-gray-800">Creating a Blob URL from Image Data</h3>
<p class="mb-4 text-gray-700 leading-relaxed">The key to displaying a Blob image in React Native is to create a URL that the <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">&lt;Image&gt;</code> component can understand. Here’s how you can do it:</p>
<ol class="list-decimal pl-6 mb-4">
<li class="mb-2 text-gray-700">
<strong>Convert your Blob to a URL:</strong> Use the <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">URL.createObjectURL(blob)</code> method. This creates a temporary URL that points to the Blob data in memory.  Think of it as creating a short-lived "address" for your image.
    </li>
<li class="mb-2 text-gray-700">
<strong>Important note about memory management:</strong> <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">URL.createObjectURL</code> creates a reference to the Blob in memory. It's crucial to release this reference when you're done with the image to prevent memory leaks. You can do this using <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">URL.revokeObjectURL(url)</code>.  Make sure you call this after the image is unmounted or when you know you no longer need it.
    </li>
<li class="mb-2 text-gray-700">
<strong>Example Code:</strong>
<p class="mb-4 text-gray-700 leading-relaxed">Here's a snippet demonstrating the process:</p>
<pre class="bg-gray-800 text-white text-sm p-4 rounded overflow-auto mb-4"><code class="bg-gray-100 text-sm px-1 py-0.5 rounded">
const [imageUrl, setImageUrl] = React.useState(null);

React.useEffect(() =&gt; {
  if (blobData) { // Assuming you have the blob data in a variable called blobData
    const url = URL.createObjectURL(blobData);
    setImageUrl(url);

    return () =&gt; URL.revokeObjectURL(url); // Cleanup when component unmounts
  }
}, [blobData]);
      </code></pre>
</li>
</ol>
<h3 class="text-xl font-semibold mb-3 text-gray-800">Using Blob URLs with the &lt;Image&gt; Component</h3>
<p class="mb-4 text-gray-700 leading-relaxed">Now that you have a Blob URL, you can use it just like any other image URL in your React Native app. Here’s how:</p>
<ul class="list-disc pl-6 mb-4">
<li class="mb-2 text-gray-700">
<strong>Pass the URL to the <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">source</code> prop:</strong> The <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">&lt;Image&gt;</code> component's <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">source</code> prop accepts a URI.
    </li>
<li class="mb-2 text-gray-700">
<strong>Example Usage:</strong>
<pre class="bg-gray-800 text-white text-sm p-4 rounded overflow-auto mb-4"><code class="bg-gray-100 text-sm px-1 py-0.5 rounded">
&lt;Image
  source={{ uri: imageUrl }}
  style={{ width: 200, height: 200 }}
/&gt;
      </code></pre>
</li>
<li class="mb-2 text-gray-700">
<strong>Styling:</strong> Remember to style your <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">&lt;Image&gt;</code> component appropriately to ensure the image is displayed correctly.  Use <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">width</code> and <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">height</code> styles to control its dimensions.
    </li>
</ul>
<h3 class="text-xl font-semibold mb-3 text-gray-800">Handling Different Image Formats (JPEG, PNG, etc.)</h3>
<p class="mb-4 text-gray-700 leading-relaxed">Blobs can contain images of various formats. Ensure your app handles them correctly.  The key is to ensure your Blob's <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">type</code> property is correctly set. This type information will guide React Native to render the image properly.</p>
<ul class="list-disc pl-6 mb-4">
<li class="mb-2 text-gray-700">
<strong>Check the <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">Content-Type</code>:</strong> When you fetch the image data (e.g., using <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">XMLHttpRequest</code> - <a class="text-purple-600 hover:text-purple-800 transition-colors" href="https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/send">XMLHttpRequest.send() - Web APIs | MDN</a>), the server should send a <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">Content-Type</code> header. This tells you the image format (e.g., <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">image/jpeg</code>, <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">image/png</code>).
    </li>
<li class="mb-2 text-gray-700">
<strong>Set the Blob's <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">type</code> property:</strong> When constructing the Blob, pass the <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">Content-Type</code> as the <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">type</code> option:
        <pre class="bg-gray-800 text-white text-sm p-4 rounded overflow-auto mb-4"><code class="bg-gray-100 text-sm px-1 py-0.5 rounded">
const blob = new Blob([imageData], { type: 'image/jpeg' }); // Replace image/jpeg with the correct content type
        </code></pre>
</li>
<li class="mb-2 text-gray-700">
<strong>Default Handling:</strong> React Native generally handles common image formats without needing extra configuration. However, if you encounter issues, double-check the <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">Content-Type</code> and Blob type.
    </li>
</ul>
<h3 class="text-xl font-semibold mb-3 text-gray-800">Troubleshooting Common Display Issues</h3>
<p class="mb-4 text-gray-700 leading-relaxed">Sometimes, things don't go as planned. Here are a few common issues and how to tackle them:</p>
<ul class="list-disc pl-6 mb-4">
<li class="mb-2 text-gray-700">
<strong>Image Not Displaying:</strong>
<ul class="list-disc pl-6 mb-4">
<li class="mb-2 text-gray-700"><strong>Check the URL:</strong> Ensure the Blob URL is valid and accessible.  Log it to the console to verify.</li>
<li class="mb-2 text-gray-700"><strong>Verify Blob Data:</strong> Make sure the Blob actually contains image data and isn't empty or corrupted.  Check the size of the blob.</li>
<li class="mb-2 text-gray-700"><strong>CORS Issues:</strong> If you're fetching the image from a different domain, ensure CORS (Cross-Origin Resource Sharing) is configured correctly on the server.</li>
<li class="mb-2 text-gray-700"><strong>Style Problems:</strong> The image might be displaying, but not visible due to incorrect styling (e.g., zero width/height, overflowing a hidden container).</li>
</ul>
</li>
<li class="mb-2 text-gray-700">
<strong>Memory Leaks:</strong>
<ul class="list-disc pl-6 mb-4">
<li class="mb-2 text-gray-700"><strong>Always revoke the URL:</strong> As mentioned earlier, <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">URL.revokeObjectURL</code> is crucial.  Make absolutely certain you're calling it when the component unmounts or when the image is no longer needed.  A simple mistake here will lead to memory issues!</li>
</ul>
</li>
<li class="mb-2 text-gray-700">
<strong>Performance Issues:</strong>
<ul class="list-disc pl-6 mb-4">
<li class="mb-2 text-gray-700"><strong>Large Images:</strong> Very large images can impact performance. Consider resizing or optimizing images before displaying them.</li>
<li class="mb-2 text-gray-700"><strong>Caching:</strong> Implement caching to avoid repeatedly creating Blob URLs for the same image.</li>
</ul>
</li>
</ul>
<figure class="my-6 flex flex-col items-center">
<img alt="React Native app interface displaying blob image handling code example." class="w-full h-64 object-cover rounded-lg mb-4" loading="lazy" src="https://images.unsplash.com/photo-1656248396925-ec086a35c568?crop=entropy&amp;cs=tinysrgb&amp;fit=max&amp;fm=jpg&amp;ixid=M3w3NDkxMTJ8MHwxfHNlYXJjaHwxfHxSZWFjdCUyMG5hdGl2ZSUyMGJsb2IlMjBpbWFnZSUyMFJlYWN0JTIwTmF0aXZlJTIwQmxvYiUyMEltYWdlcyUzQSUyMENvbXByZWhlbnNpdmUlMjBndWlkZSUyMG9uJTIwaGFuZGxpbmdkJTIwYmxvYiUyMGltYWdlcy4lMjBDb3ZlciUyMGRpc3BsYXklMkMlMjB1cGxvYWQlMkMlMjBwZXJmb3JtYW5jZSUyQyUyMGNhY2hpbmclMkMlMjBhbmQlMjBjb252ZXJzaW9ucyUyMCUyOGJhc2U2NCUyQyUyMGZpbGUlMjkuJTIwQWRkcmVzcyUyMGNvbW1vbiUyMGlzc3VlcyUyMCUyNiUyMHNvbHV0aW9ucy58ZW58MHx8fHwxNzUyNTEzMzgzfDA&amp;ixlib=rb-4.1.0&amp;q=80&amp;w=1080"/>
<figcaption class="text-sm text-gray-500 italic mt-2">Example implementation of React Native Blob Image display</figcaption>
</figure>
</section>
            <!--Section two -->
            <section id="heading='Optimizing React Native Blob Image Performance' subheadings=[]">
<h2 class="text-2xl font-bold mb-4 text-gray-800">Optimizing React Native Blob Image Performance</h2>
<p class="mb-4 text-gray-700 leading-relaxed">Okay, we've got our React Native app displaying blob images – awesome! But let's be real, sometimes things can get a little sluggish. Big images, slow network connections... it all adds up. In this section, I'm going to walk you through some techniques I've learned to keep your app running smoothly, even when dealing with hefty blob images.</p>
<p class="mb-4 text-gray-700 leading-relaxed">One of the biggest culprits for performance issues is unnecessary re-renders. React Native's <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">Image</code> component, just like any other React component, will re-render if its props change. That means if you're creating a new blob URL every time the component updates, you're forcing the image to reload – even if the actual image data hasn't changed!  The React Native Image Component can be found <a class="text-purple-600 hover:text-purple-800 transition-colors" href="https://reactnative.dev/docs/image">here</a>.</p>
<p class="mb-4 text-gray-700 leading-relaxed">So, how do we avoid this? Memoization is your friend! By using techniques like <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">React.memo</code> or <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">useMemo</code>, you can prevent the component from re-rendering unless the relevant data actually changes. Here's a simplified example:</p>
<code class="bg-gray-100 text-sm px-1 py-0.5 rounded">
<pre class="bg-gray-800 text-white text-sm p-4 rounded overflow-auto mb-4">
      <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">
const MemoizedImage = React.memo(function ImageComponent({ blobUrl }) {
  return &lt;Image source={{ uri: blobUrl }} style={{ width: 200, height: 200 }} /&gt;;
});

// Later, in your component:
const blobUrl = useMemo(() =&gt; createObjectURL(myBlob), [myBlob]);

&lt;MemoizedImage blobUrl={blobUrl} /&gt;
      </code>
    </pre>
</code>
<p class="mb-4 text-gray-700 leading-relaxed">In this example, <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">useMemo</code> ensures that <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">blobUrl</code> is only re-calculated when <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">myBlob</code> changes. And <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">React.memo</code> ensures that <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">ImageComponent</code> only re-renders if <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">blobUrl</code> changes.  That alone can make a HUGE difference!</p>
<p class="mb-4 text-gray-700 leading-relaxed">Beyond preventing re-renders, consider optimizing the images themselves.  Are you displaying full-resolution images when thumbnails would suffice?  Downsampling images before displaying them is a common, but important optimization.  Also, ensure your server delivers compressed images. There are also libraries such as <a class="text-purple-600 hover:text-purple-800 transition-colors" href="https://www.npmjs.com/package/react-native-image-picker">react-native-image-picker</a>, and <a class="text-purple-600 hover:text-purple-800 transition-colors" href="https://docs.expo.dev/versions/latest/sdk/image/">Expo Image API</a> to assist you.</p>
<div class="mb-4">
<h4 class="text-lg font-medium text-indigo-500 mb-2">Key Insight</h4>
<p class="mb-4 text-gray-700 leading-relaxed">Caching image data is crucial! Once you've loaded a blob image, store it in memory or on disk. This way, you can quickly retrieve it later without having to re-download or re-process it.  Consider using a dedicated caching library for more advanced control.</p>
</div>
<p class="mb-4 text-gray-700 leading-relaxed">Finally, think about how you're fetching your blobs in the first place. Asynchronously retrieving blobs in chunks using <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">XMLHttpRequest.send()</code> (more information available at <a class="text-purple-600 hover:text-purple-800 transition-colors" href="https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/send">XMLHttpRequest.send() - Web APIs | MDN</a>) can prevent the UI from blocking, giving the user a much smoother experience. Be sure to check <a class="text-purple-600 hover:text-purple-800 transition-colors" href="https://developer.mozilla.org/en-US/docs/Web/API/File/Using_files_from_web_applications">Using files from web applications - Web APIs | MDN</a>.</p>
<p class="mb-4 text-gray-700 leading-relaxed">By implementing these techniques, I've seen significant improvements in React Native blob image performance. It's all about being mindful of re-renders, optimizing images, and fetching data efficiently. Give them a try and see what works best for your app!</p>
<figure class="my-6 flex flex-col items-center">
<img alt="Abstract digital art representing React Native blob image processing and data transfer." class="w-full h-64 object-cover rounded-lg mb-4" loading="lazy" src="https://images.unsplash.com/photo-1712230879699-e8a0a389da63?crop=entropy&amp;cs=tinysrgb&amp;fit=max&amp;fm=jpg&amp;ixid=M3w3NDkxMTJ8MHwxfHNlYXJjaHwyfHxSZWFjdCUyMG5hdGl2ZSUyMGJsb2IlMjBpbWFnZSUyMFJlYWN0JTIwTmF0aXZlJTIwQmxvYiUyMEltYWdlcyUzQSUyMENvbXByZWhlbnNpdmUlMjBndWlkZSUyMG9uJTIwaGFuZGxpbmclMjBibG9iJTIwaW1hZ2VzLiUyMENvdmVyJTIwZGlzcGxheSUyQyUyMHVwbG9hZCUyQyUyMHBlcmZvcm1hbmNlJTJDJTIwY2FjaGluZyUyQyUyMGFuZCUyMGNvbnZlcnNpb25zJTIwJTI4YmFzZTY0JTJDJTIwZmlsZSUyOS4lMjBBZGRyZXNzJTIwY29tbW9uJTIwaXNzdWVzJTIwJTI2JTIwc29sdXRpb25zLnxlbnwwfHx8fDE3NTI1MTMzODR8MA&amp;ixlib=rb-4.1.0&amp;q=80&amp;w=1080"/>
<figcaption class="text-sm text-gray-500 italic mt-2">Optimizing data transfer and processing leads to smoother image rendering.</figcaption>
</figure>
</section>
            <!--Section three -->
            <section id="blob-image-conversion-and-handling">
<h2 class="text-2xl font-bold mb-4 text-gray-800">Blob Image Conversion and Handling</h2>
<p class="mb-4 text-gray-700 leading-relaxed">Now that we've covered the basics, let's dive into the practical side of things: converting different data types into blob images and handling them in React Native. This is where things get really interesting!</p>
<h3 class="text-xl font-semibold mb-3 text-gray-800">Converting Base64 to Blob Images</h3>
<p class="mb-4 text-gray-700 leading-relaxed">Often, you'll receive image data as a Base64 encoded string. To display this in a React Native <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">&lt;Image&gt;</code> component, you need to convert it into a blob. Here's how I usually tackle this:</p>
<pre class="bg-gray-800 text-white text-sm p-4 rounded overflow-auto mb-4">
    <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">
      const base64ToBlob = async (base64String) =&gt; {
        const byteCharacters = atob(base64String.split(',')[1]);
        const byteArrays = [];

        for (let offset = 0; offset &lt; byteCharacters.length; offset += 512) {
          const slice = byteCharacters.slice(offset, offset + 512);

          const byteNumbers = new Array(slice.length);
          for (let i = 0; i &lt; slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
          }

          const byteArray = new Uint8Array(byteNumbers);
          byteArrays.push(byteArray);
        }

        const blob = new Blob(byteArrays, { type: 'image/jpeg' }); // Adjust type as needed
        return blob;
      };
    </code>
  </pre>
<p class="mb-4 text-gray-700 leading-relaxed">Then you can create an object URL:</p>
<pre class="bg-gray-800 text-white text-sm p-4 rounded overflow-auto mb-4">
    <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">
      const blob = await base64ToBlob(base64String);
      const imageUrl = URL.createObjectURL(blob);
      setImageSource({ uri: imageUrl });
    </code>
  </pre>
<p class="mb-4 text-gray-700 leading-relaxed">This creates a temporary URL that React Native can use to display the image.  Remember to revoke the URL when you're done to free up memory using <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">URL.revokeObjectURL(imageUrl)</code></p>
<h3 class="text-xl font-semibold mb-3 text-gray-800">Converting Files to Blob Images</h3>
<p class="mb-4 text-gray-700 leading-relaxed">Sometimes, you might have direct access to a file (especially in web environments or after using certain libraries). Converting this file to a blob is straightforward:</p>
<pre class="bg-gray-800 text-white text-sm p-4 rounded overflow-auto mb-4">
    <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">
      const fileToBlob = async (file) =&gt; {
        const blob = new Blob([file], { type: file.type });
        return blob;
      };
    </code>
  </pre>
<p class="mb-4 text-gray-700 leading-relaxed">This is particularly useful when integrating with web-based APIs or when using file upload components.</p>
<figure class="my-6 flex flex-col items-center">
<img alt="Mobile phone displaying a React Native application loading a blob image." class="w-full h-64 object-cover rounded-lg mb-4" loading="lazy" src="https://images.unsplash.com/photo-1589828994379-7a8869c4f519?crop=entropy&amp;cs=tinysrgb&amp;fit=max&amp;fm=jpg&amp;ixid=M3w3NDkxMTJ8MHwxfHNlYXJjaHwzfHxSZWFjdCUyMG5hdGl2ZSUyMGJsb2IlMjBpbWFnZSUyMFJlYWN0JTIwTmF0aXZlJTIwQmxvYiUyMEltYWdlcyUzQSUyMENvbXByZWhlbnNpdmUlMjBndWlkZSUyMG9uJTIwaGFuZGxpbmclMjBibG9iJTIwaW1hZ2VzLiUyMENvdmVyJTIwZGlzcGxheSUyQyUyMHVwbG9hZCUyQyUyMHBlcmZvcm1hbmNlJTJDJTIwY2FjaGluZyUyQyUyMGFuZCUyMGNvbnZlcnNpb25zJTIwJTI4YmFzZTY0JTJDJTIwZmlsZSUyOS4lMjBBZGRyZXNzJTIwY29tbW9uJTIwaXNzdWVzJTIwJTI2JTIwc29sdXRpb25zLnxlbnwwfHx8fDE3NTI1MTMzODR8MA&amp;ixlib=rb-4.1.0&amp;q=80&amp;w=1080"/>
<figcaption class="text-sm text-gray-500 italic mt-2">See your blob images come to life in your React Native app!</figcaption>
</figure>
<h3 class="text-xl font-semibold mb-3 text-gray-800">React Native Image Picker and Blobs</h3>
<p class="mb-4 text-gray-700 leading-relaxed">A common use case is getting images from the user's device.  I've found the <a class="text-purple-600 hover:text-purple-800 transition-colors" href="https://www.npmjs.com/package/react-native-image-picker" rel="noopener noreferrer" target="_blank"><code class="bg-gray-100 text-sm px-1 py-0.5 rounded">react-native-image-picker</code></a> library very handy for this.  After picking an image, you might want to convert it to a blob, especially if you're uploading it to a server that expects blob data.</p>
<p class="mb-4 text-gray-700 leading-relaxed">The image picker usually returns a file path or Base64 representation. If you get a file path, you can use the <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">fetch</code> API to get a blob:</p>
<pre class="bg-gray-800 text-white text-sm p-4 rounded overflow-auto mb-4">
    <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">
      const pickImageAndConvertToBlob = async () =&gt; {
        const result = await launchImageLibrary(); // Using react-native-image-picker

        if (result.didCancel) {
          return;
        }

        const imageUri = result.assets[0].uri;

        try {
          const response = await fetch(imageUri);
          const blob = await response.blob();
          setImageSource({ uri: URL.createObjectURL(blob) });
        } catch (error) {
          console.error("Error creating blob:", error);
        }
      };
    </code>
  </pre>
<p class="mb-4 text-gray-700 leading-relaxed">Don't forget error handling! It's crucial to handle cases where the image picker fails or the file can't be read.</p>
<h3 class="text-xl font-semibold mb-3 text-gray-800">Uploading Blob Images</h3>
<p class="mb-4 text-gray-700 leading-relaxed">Finally, let's talk about uploading blob images to a server.  The key here is to use <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">XMLHttpRequest</code> or the <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">fetch</code> API with the correct headers.</p>
<p class="mb-4 text-gray-700 leading-relaxed">Here's an example using <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">fetch</code>:</p>
<pre class="bg-gray-800 text-white text-sm p-4 rounded overflow-auto mb-4">
    <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">
      const uploadBlob = async (blob) =&gt; {
        const formData = new FormData();
        formData.append('image', blob, 'image.jpg'); // Adjust filename and extension as needed

        try {
          const response = await fetch('YOUR_UPLOAD_ENDPOINT', {
            method: 'POST',
            body: formData,
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });

          if (response.ok) {
            console.log('Upload successful!');
          } else {
            console.error('Upload failed:', response.status);
          }
        } catch (error) {
          console.error('Error uploading:', error);
        }
      };
    </code>
  </pre>
<p class="mb-4 text-gray-700 leading-relaxed">Make sure your server is configured to handle multipart form data.  The <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">'Content-Type': 'multipart/form-data'</code> header is crucial.</p>
<div class="mb-4">
<h4 class="text-lg font-medium text-indigo-500 mb-2">Quick Tip</h4>
<p class="mb-4 text-gray-700 leading-relaxed">Always check the <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">type</code> property of your blob and ensure it matches the expected MIME type of your image (e.g., <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">image/jpeg</code>, <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">image/png</code>). Mismatched types can lead to display or upload errors.</p>
</div>
<div class="mb-4">
<div class="mb-4">
<h4 class="text-lg font-medium text-indigo-500 mb-2">Do:</h4>
<p class="mb-4 text-gray-700 leading-relaxed">✅ Use <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">URL.revokeObjectURL</code> to release memory after the image is displayed.</p>
<p class="mb-4 text-gray-700 leading-relaxed">✅ Handle errors gracefully, especially during image picking and uploading.</p>
<p class="mb-4 text-gray-700 leading-relaxed">✅ Set the correct <code class="bg-gray-100 text-sm px-1 py-0.5 rounded">Content-Type</code> when uploading blobs.</p>
</div>
<div class="mb-4">
<h4 class="text-lg font-medium text-indigo-500 mb-2">Don't:</h4>
<p class="mb-4 text-gray-700 leading-relaxed">❌ Forget to specify the image type when creating a blob.</p>
<p class="mb-4 text-gray-700 leading-relaxed">❌ Assume the user will always pick an image; handle cancellation cases.</p>
<p class="mb-4 text-gray-700 leading-relaxed">❌ Hardcode file names or extensions when uploading; use dynamic values.</p>
</div>
</div>
</section>
            <!--Section Four -->
            <section id="heading='Advanced Blob Image Management' subheadings=['Blob Image Caching Strategies', 'Implementing a Blob Image Viewer', 'Addressing Memory Management Concerns']">
<h2 class="text-2xl font-bold mb-4 text-gray-800">Advanced Blob Image Management</h2>
<ul class="list-disc pl-6 mb-4">
<li class="mb-2 text-gray-700">Now that you are comfortable displaying Blob images in your React Native app, let's dive into more advanced techniques for optimizing performance and user experience.</li>
<li class="mb-2 text-gray-700">We will explore caching strategies, image viewers, and memory management.</li>
</ul>
<h3 class="text-xl font-semibold mb-3 text-gray-800">Blob Image Caching Strategies</h3>
<ul class="list-disc pl-6 mb-4">
<li class="mb-2 text-gray-700">
      Caching Blob images is crucial for improving performance, especially when dealing with large images or slow network connections. By caching images, you reduce the need to repeatedly fetch the same data, resulting in faster load times and a smoother user experience.
    </li>
<li class="mb-2 text-gray-700">
      One simple approach is to use React Native's AsyncStorage to store the Blob data as a string.
    </li>
<li class="mb-2 text-gray-700">
      Another option is to use a dedicated caching library like `react-native-cached-image`. Although not actively maintained, the core idea is still relevant: store the Blob data locally and check for its existence before making a network request.
    </li>
<li class="mb-2 text-gray-700">
      Remember to implement a cache invalidation strategy to ensure that users always see the latest version of the image.
    </li>
</ul>
<h3 class="text-xl font-semibold mb-3 text-gray-800">Implementing a Blob Image Viewer</h3>
<ul class="list-disc pl-6 mb-4">
<li class="mb-2 text-gray-700">
      Sometimes, you might want to provide users with a dedicated image viewer to zoom, pan, and rotate Blob images.
    </li>
<li class="mb-2 text-gray-700">
      You can build a simple image viewer using React Native's ScrollView and transform styles.  Wrap your `<image/>` component with a `<scrollview>` component.
    </scrollview></li>
<li class="mb-2 text-gray-700">
      For more advanced features, consider using a library like `react-native-image-zoom-viewer`.  This requires you to convert your Blob to a base64 string, create a URI and pass it as the `source` for the React Native `<image>` tag, as described on the <a class="text-purple-600 hover:text-purple-800 transition-colors" href="https://reactnative.dev/docs/image">React Native Image Component</a> documentation.
    </image></li>
<li class="mb-2 text-gray-700">Consider using the <a class="text-purple-600 hover:text-purple-800 transition-colors" href="https://docs.expo.dev/versions/latest/sdk/image/">Expo Image API</a> for enhanced image loading and caching capabilities, especially if you are working within the Expo ecosystem.</li>
</ul>
<h3 class="text-xl font-semibold mb-3 text-gray-800">Addressing Memory Management Concerns</h3>
<ul class="list-disc pl-6 mb-4">
<li class="mb-2 text-gray-700">
      Working with Blob images can be memory-intensive, especially on lower-end devices.  It is important to implement strategies to minimize memory usage and prevent app crashes.
    </li>
<li class="mb-2 text-gray-700">
      One important step is to release the Blob URL after the image is no longer needed.  You can do this by setting the `source` of the `<image/>` component to `null`.
    </li>
<li class="mb-2 text-gray-700">
      Avoid creating unnecessary copies of the Blob data.  Instead, pass the Blob URL directly to the `<image/>` component.
    </li>
<li class="mb-2 text-gray-700">Consider resizing images to smaller dimensions before displaying them, especially if you are dealing with high-resolution images and are picking them from a camera using packages like <a class="text-purple-600 hover:text-purple-800 transition-colors" href="https://www.npmjs.com/package/react-native-image-picker">react-native-image-picker</a>, which may give you larger images than you intended.</li>
</ul>
</section>
            <!--conclusion -->
            <section id="Conclusion">
<h2 class="text-2xl font-bold mb-4 text-gray-800">Conclusion</h2>
<p class="mb-4 text-gray-700 leading-relaxed">So, there you have it! We've journeyed together through the fascinating world of React Native Blob Images and how to display them effectively in your applications. Remember, the key takeaways are understanding the Blob format itself, knowing how to convert your images into Blobs, and mastering the art of displaying them using either the `Image` component with a Blob URL or employing `FileReader` for Base64 encoding. It might seem a little complex at first, but with practice and experimentation, you'll be displaying Blob images like a seasoned pro. The flexibility and control this approach offers are truly invaluable, especially when dealing with images sourced from various locations or requiring specific manipulation before rendering. Embrace the Blob, and watch your React Native image handling capabilities reach new heights! I hope this article gave you a solid understanding of what it takes to display React Native blob images!</p>
</section>
            
    <!---END MAGIC--->

    <!---FAQ--->
    
    <section id="faq" class="mb-12">
        <div class="flex items-center mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 mr-2 text-purple-600">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                <path d="M12 17h.01"></path>
            </svg>
            <h2 class="text-2xl font-bold text-gray-800">Frequently Asked Questions</h2>
        </div>
        <div class="border rounded-lg">
    
        <details class="group px-4 py-3 border-b">
              <summary class="flex items-center justify-between cursor-pointer list-none text-left hover:text-purple-700 font-medium">
                What is a Blob image in React Native?
                <svg class="w-5 h-5 ml-2 transform group-open:rotate-180" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </summary>
              <div class="mt-3 text-gray-700">
              A Blob (Binary Large Object) represents raw, immutable data, often used to handle image data retrieved from sources like APIs or the file system in React Native.
              </div>
            </details>
        
        <details class="group px-4 py-3 border-b">
              <summary class="flex items-center justify-between cursor-pointer list-none text-left hover:text-purple-700 font-medium">
                How do I display a React Native Blob image?
                <svg class="w-5 h-5 ml-2 transform group-open:rotate-180" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </summary>
              <div class="mt-3 text-gray-700">
              Convert the Blob to a data URL (using FileReader or similar) or upload it to a server to get a URL, then use the `<Image>` component with the generated URL as the source.
              </div>
            </details>
        
        <details class="group px-4 py-3 border-b">
              <summary class="flex items-center justify-between cursor-pointer list-none text-left hover:text-purple-700 font-medium">
                What are some performance considerations when working with React Native Blob images?
                <svg class="w-5 h-5 ml-2 transform group-open:rotate-180" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </summary>
              <div class="mt-3 text-gray-700">
              Large Blob images can impact performance. Optimize image sizes before converting to Blobs, use caching mechanisms, and consider displaying lower-resolution versions initially.
              </div>
            </details>
        
        <details class="group px-4 py-3 border-b">
              <summary class="flex items-center justify-between cursor-pointer list-none text-left hover:text-purple-700 font-medium">
                How can I convert a base64 string to a Blob image in React Native?
                <svg class="w-5 h-5 ml-2 transform group-open:rotate-180" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </summary>
              <div class="mt-3 text-gray-700">
              You can use the `atob()` function to decode the base64 string, then create a `Uint8Array` from the decoded data, and finally construct a Blob from the array.
              </div>
            </details>
        
        <details class="group px-4 py-3 border-b">
              <summary class="flex items-center justify-between cursor-pointer list-none text-left hover:text-purple-700 font-medium">
                How can I cache React Native Blob images?
                <svg class="w-5 h-5 ml-2 transform group-open:rotate-180" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </summary>
              <div class="mt-3 text-gray-700">
              Implement a caching strategy using libraries like `react-native-fs` to store Blob data locally and retrieve it for subsequent display, reducing network requests.
              </div>
            </details>
        </div></section>
    <!---END FAQ--->

    <!---EXTERNAL LINKS--->
    
            <section class="rounded-lg border p-6 bg-gray-50 mb-12">
                <div class="flex items-center mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" 
                         stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                         class="h-6 w-6 mr-2 text-purple-600">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                        <polyline points="15 3 21 3 21 9"></polyline>
                        <line x1="10" y1="14" x2="21" y2="3"></line>
                    </svg>
                    <h2 class="text-2xl font-bold text-gray-800">External Resources</h2>
                </div>

                <div class="space-y-5">
            
                <div class="border-b border-gray-200 last:border-0 pb-4 last:pb-0">
                    <a 
                        href="https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/send" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        class="text-lg font-medium text-purple-700 hover:text-purple-900 transition-colors flex items-center"
                    >
                        XMLHttpRequest.send() - Web APIs | MDN
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" 
                             stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                             class="ml-1 h-4 w-4">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                        </svg>
                    </a>
                </div>
                
                <div class="border-b border-gray-200 last:border-0 pb-4 last:pb-0">
                    <a 
                        href="https://developer.mozilla.org/en-US/docs/Web/API/File/Using_files_from_web_applications" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        class="text-lg font-medium text-purple-700 hover:text-purple-900 transition-colors flex items-center"
                    >
                        Using files from web applications - Web APIs | MDN
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" 
                             stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                             class="ml-1 h-4 w-4">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                        </svg>
                    </a>
                </div>
                
                <div class="border-b border-gray-200 last:border-0 pb-4 last:pb-0">
                    <a 
                        href="https://www.npmjs.com/package/react-native-image-picker" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        class="text-lg font-medium text-purple-700 hover:text-purple-900 transition-colors flex items-center"
                    >
                        react-native-image-picker - npm
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" 
                             stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                             class="ml-1 h-4 w-4">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                        </svg>
                    </a>
                </div>
                
                <div class="border-b border-gray-200 last:border-0 pb-4 last:pb-0">
                    <a 
                        href="https://docs.expo.dev/versions/latest/sdk/image/" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        class="text-lg font-medium text-purple-700 hover:text-purple-900 transition-colors flex items-center"
                    >
                        Expo Image API
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" 
                             stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                             class="ml-1 h-4 w-4">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                        </svg>
                    </a>
                </div>
                
                <div class="border-b border-gray-200 last:border-0 pb-4 last:pb-0">
                    <a 
                        href="https://reactnative.dev/docs/image" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        class="text-lg font-medium text-purple-700 hover:text-purple-900 transition-colors flex items-center"
                    >
                        React Native Image Component
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" 
                             stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                             class="ml-1 h-4 w-4">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                        </svg>
                    </a>
                </div>
                
                </div>
            </section>
            
    <!---END EXTERNAL LINKS--->
    
</body>
</html>