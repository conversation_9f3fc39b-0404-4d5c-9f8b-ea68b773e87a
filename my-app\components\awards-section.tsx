"use client"

import { motion } from "framer-motion"
import {
  Code,
  Server,
  Database,
  Layout,
  Zap,
  Lightbulb,
  Smartphone,
  GitBranch,
  CheckCircle,
  GitMerge,
  Kanban,
  Brain,
  Users,
  Settings,
  TestTube,
} from "lucide-react"
import { JSX } from "react"

// Map of icon names to components
const iconMap: Record<string, JSX.Element> = {
  code: <Code className="w-6 h-6" />,
  server: <Server className="w-6 h-6" />,
  database: <Database className="w-6 h-6" />,
  layout: <Layout className="w-6 h-6" />,
  zap: <Zap className="w-6 h-6" />,
  lightbulb: <Lightbulb className="w-6 h-6" />,
  smartphone: <Smartphone className="w-6 h-6" />,
  "git-branch": <GitBranch className="w-6 h-6" />,
  "check-circle": <CheckCircle className="w-6 h-6" />,
  "git-merge": <GitMerge className="w-6 h-6" />,
  trello: <Kanban className="w-6 h-6" />,
  brain: <Brain className="w-6 h-6" />,
  users: <Users className="w-6 h-6" />,
  settings: <Settings className="w-6 h-6" />,
  "test-tube": <TestTube className="w-6 h-6" />,
}

// Static awards data
const awards = [
  {
    id: 1,
    year: "2025",
    title: "Technical Workshop Host",
    subtitle: "From React to React Native",
    description: "Delivered a hands-on workshop to guide web developers transitioning from React.js to mobile development using React Native.",
    icon: "users",
    color: "text-blue-500",
    category: "Technical Leadership",
  },
  {
    id: 2,
    year: "2024",
    title: "Technical Writer",
    subtitle: "Medium",
    description: "Authored two articles sharing my transition from Student to Mobile developer on Medium platform.",
    icon: "lightbulb",
    color: "text-green-500",
    category: "Content Creation",
  },
  {
    id: 3,
    year: "2024",
    title: "Open Source Contributor",
    subtitle: "Analytiks SDK",
    description: "Contributed to the development of a public analytics SDK, enhancing logging and data capture features for Android apps.",
    icon: "code",
    color: "text-purple-500",
    category: "Open Source",
  },
  {
    id: 4,
    year: "2022",
    title: "Project Leader",
    subtitle: "Student Housing Rental Web App",
    description: "Led the full-stack development and team coordination of a MERN-based web application for student apartment rentals in Marrakech.",
    icon: "trello",
    color: "text-orange-500",
    category: "Project Management",
  },
  {
    id: 5,
    year: "2021",
    title: "Campus Director",
    subtitle: "Hult Prize ENSA",
    description: "Organized and led the local edition of the global Hult Prize social entrepreneurship competition, mentoring student teams and coordinating with international representatives.",
    icon: "users",
    color: "text-emerald-500",
    category: "Leadership",
  },
]

export default function AwardsSection() {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  }

  return (
    <div className="min-h-screen p-8 md:p-12 bg-gradient-to-br from-amber-50 to-orange-100 dark:from-gray-900 dark:to-amber-950">
      <div className="max-w-6xl mx-auto">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-4xl md:text-5xl font-bold mb-6 text-center lg:text-left"
        >
          Awards & <span className="text-amber-600 dark:text-amber-400">Recognition</span>
        </motion.h2>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-lg text-muted-foreground mb-12 text-center lg:text-left"
        >
          Achievements, contributions, and recognition in the developer community
        </motion.p>

        {/* Awards Grid */}
        <motion.div
          variants={container}
          initial="hidden"
          animate="show"
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
        >
          {awards.map((award) => (
            <motion.div
              key={award.id}
              variants={item}
              className="group relative overflow-hidden rounded-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="p-8">
                <div className="flex items-start gap-6">
                  <div className={`${award.color} mt-2 group-hover:scale-110 transition-transform duration-300`}>
                    {iconMap[award.icon.toLowerCase()] || <Code className="w-6 h-6" />}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="text-xs bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200 px-3 py-1 rounded-full font-medium">
                        {award.year}
                      </span>
                      <span className="text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 px-3 py-1 rounded-full">
                        {award.category}
                      </span>
                    </div>
                    <h3 className="text-xl font-bold mb-2 group-hover:text-amber-600 dark:group-hover:text-amber-400 transition-colors duration-300">
                      {award.title}
                    </h3>
                    <p className="text-sm font-semibold text-amber-600 dark:text-amber-400 mb-4">
                      {award.subtitle}
                    </p>
                    <p className="text-muted-foreground leading-relaxed">
                      {award.description}
                    </p>
                  </div>
                </div>
              </div>
              
              {/* Decorative border */}
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-500 to-orange-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-16 text-center"
        >
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-8 border border-amber-200 dark:border-amber-800">
            <h3 className="text-2xl font-bold mb-4">Interested in Collaboration?</h3>
            <p className="text-muted-foreground mb-6">
              I m always open to new opportunities, technical discussions, and knowledge sharing.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://medium.com/@yessinejawa"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-6 py-3 bg-amber-600 hover:bg-amber-700 text-white rounded-full font-medium transition-colors duration-300"
              >
                Read My Articles
              </a>
              <a
                href="https://github.com/apachi1444"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-6 py-3 border border-amber-600 text-amber-600 hover:bg-amber-600 hover:text-white rounded-full font-medium transition-colors duration-300"
              >
                View My Code
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
