"use client"

import { motion } from "framer-motion"
import BlogPostCard from "./blog-post-card"

const blogPosts = [
  {
    id: 1,
    title: "Building Responsive UIs with Tailwind CSS",
    excerpt:
      "Learn how to create beautiful, responsive user interfaces using Tailwind CSS, a utility-first CSS framework.",
    image: "/placeholder.svg?height=600&width=800",
    category: "UI/UX",
    date: "March 15, 2023",
    readTime: "5 min read",
    slug: "building-responsive-uis-with-tailwind",
  },
  {
    id: 2,
    title: "Getting Started with React Native and Expo",
    excerpt:
      "A comprehensive guide to setting up your first React Native project with Expo and building cross-platform mobile apps.",
    image: "/placeholder.svg?height=600&width=800",
    category: "Mobile Development",
    date: "April 22, 2023",
    readTime: "8 min read",
    slug: "getting-started-with-react-native-and-expo",
  },
  {
    id: 3,
    title: "The Power of Server Components in Next.js",
    excerpt: "Explore how Server Components in Next.js can improve performance and simplify your React applications.",
    image: "/placeholder.svg?height=600&width=800",
    category: "Web Development",
    date: "May 10, 2023",
    readTime: "6 min read",
    slug: "power-of-server-components-in-nextjs",
  },
  {
    id: 4,
    title: "Optimizing Performance in React Applications",
    excerpt: "Learn techniques and best practices for improving the performance of your React applications.",
    image: "/placeholder.svg?height=600&width=800",
    category: "Performance",
    date: "June 5, 2023",
    readTime: "7 min read",
    slug: "optimizing-performance-in-react-applications",
  },
  {
    id: 5,
    title: "Creating Animations with Framer Motion",
    excerpt: "A guide to implementing smooth, beautiful animations in your React projects using Framer Motion.",
    image: "/placeholder.svg?height=600&width=800",
    category: "UI/UX",
    date: "July 18, 2023",
    readTime: "5 min read",
    slug: "creating-animations-with-framer-motion",
  },
  {
    id: 6,
    title: "Introduction to TypeScript for JavaScript Developers",
    excerpt:
      "Discover how TypeScript can enhance your JavaScript development experience with static typing and improved tooling.",
    image: "/placeholder.svg?height=600&width=800",
    category: "Web Development",
    date: "August 30, 2023",
    readTime: "9 min read",
    slug: "introduction-to-typescript-for-javascript-developers",
  },
]

export default function BlogPostGrid() {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  }

  return (
    <motion.div
      variants={container}
      initial="hidden"
      animate="show"
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12"
    >
      {blogPosts.map((post) => (
        <motion.div key={post.id} variants={item}>
          <BlogPostCard post={post} />
        </motion.div>
      ))}
    </motion.div>
  )
}

