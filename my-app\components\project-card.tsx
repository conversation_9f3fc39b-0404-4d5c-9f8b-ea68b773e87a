"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { Button } from "./ui/button"
import { ExternalLink, Github } from "lucide-react"
import { Badge } from "./ui/badge"

interface Project {
  id: number
  title: string
  description: string
  image?: string
  image_url?: string
  tags: string[]
  demoUrl?: string
  codeUrl?: string
  demo_url?: string
  code_url?: string
}

interface ProjectCardProps {
  project: Project
  featured?: boolean
}

export default function ProjectCard({ project, featured = false }: ProjectCardProps) {
  if (featured) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
        <motion.div
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.3 }}
          className="relative aspect-video rounded-xl overflow-hidden shadow-lg"
        >
          <Image src={project.image_url || project.image || "https://via.placeholder.com/800x450.png?text=Project+Image"} alt={project.title} fill className="object-cover" />
        </motion.div>
        <div className="space-y-4">
          <h3 className="text-2xl font-semibold">{project.title}</h3>
          <p className="text-muted-foreground">{project.description}</p>
          <div className="flex flex-wrap gap-2 pt-2">
            {project.tags.map((tag) => (
              <Badge key={tag} variant="secondary">
                {tag}
              </Badge>
            ))}
          </div>
          <div className="flex gap-4 pt-4">
            <Button asChild variant="default" size="sm">
              <a href={project.demo_url || project.demoUrl || "#"} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="mr-2 h-4 w-4" />
                Live Demo
              </a>
            </Button>
            <Button asChild variant="outline" size="sm">
              <a href={project.code_url || project.codeUrl || "#"} target="_blank" rel="noopener noreferrer">
                <Github className="mr-2 h-4 w-4" />
                View Code
              </a>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
      className="bg-background rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow"
    >
      <div className="relative aspect-video">
        <Image src={project.image_url || project.image || "https://via.placeholder.com/800x450.png?text=Project+Image"} alt={project.title} fill className="object-cover" />
      </div>
      <div className="p-6 space-y-4">
        <h3 className="text-xl font-semibold">{project.title}</h3>
        <p className="text-muted-foreground line-clamp-2">{project.description}</p>
        <div className="flex flex-wrap gap-2 pt-2">
          {project.tags.map((tag) => (
            <Badge key={tag} variant="secondary">
              {tag}
            </Badge>
          ))}
        </div>
        <div className="flex gap-4 pt-4">
          <Button asChild variant="default" size="sm">
            <a href={project.demo_url || project.demoUrl || "#"} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="mr-2 h-4 w-4" />
              Live Demo
            </a>
          </Button>
          <Button asChild variant="outline" size="sm">
            <a href={project.code_url || project.codeUrl || "#"} target="_blank" rel="noopener noreferrer">
              <Github className="mr-2 h-4 w-4" />
              View Code
            </a>
          </Button>
        </div>
      </div>
    </motion.div>
  )
}

