"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "./ui/button"
import Link from "next/link"
import { ArrowRight } from "lucide-react"

export default function ContactCTA() {
  return (
    <section className="py-20 bg-muted">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to bring your ideas to life?</h2>
          <p className="text-lg text-muted-foreground mb-8">
            I&apos;m currently available for freelance work and exciting opportunities. Let&apos;s discuss how I can
            help with your next project.
          </p>
          <Button asChild size="lg" className="rounded-full">
            <Link href="/contact">
              Get in Touch
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  )
}

