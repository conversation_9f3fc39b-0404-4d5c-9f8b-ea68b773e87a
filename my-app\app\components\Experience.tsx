"use client"

import { Briefcase, Calendar, MapPin, Code, Server, Smartphone } from "lucide-react"
import Image from "next/image"
import { motion } from "framer-motion"
import AnimatedSectionHeader from "./AnimatedSectionHeader"

export default function Experience() {
  const experiences = [
    {
      company: "Xelops Technology (Ex Neoxia Maroc)",
      location: "Casablanca, Maroc",
      period: "09/2023 - En cours",
      role: "Développeur Mobile",
      projects: [
        {
          name: "Application mobile d'ocrisation",
          responsibilities: [
            "Gestion des notifications push, opérations asynchrones avec Firebase et consommation d'API REST.",
            "Mise en place d'un pipeline CI/CD Android dans Azure DevOps et automatisation du déploiement iOS sur TestFlight avec Fastlane.",
            "Création de builds IPA locaux et déploiement vers TestFlight pour tests.",
          ],
          technologies: "Kotlin, Jetpack Compose, Jenkins, Jira, Maestro, End-to-End (E2E) Testing",
        },
        {
          name: "Application mobile collaborative",
          responsibilities: [
            "Amélioration de l'architecture MVVM, refactoring du code et tests E2E/unitaires pour renforcer la robustesse de l'application.",
            "Développement d'une application avec OCR (intégration du SDK) et navigation multi-flux, en utilisant Jetpack Compose pour une interface réactive.",
          ],
          technologies: "React Native, Azure DevOps, Azure AD, Fastlane, Firebase, REST APIs, TestFlight",
        },
      ],
    },
    {
      company: "Neoxia Maroc",
      location: "Casablanca, Maroc",
      period: "02/2023 - 08/2023",
      role: "Stage PFE (6 Mois)",
      projects: [
        {
          name: "Application mobile d'ocrisation",
          responsibilities: [],
          technologies: "Kotlin",
        },
        {
          name: "Application mobile collaborative",
          responsibilities: [],
          technologies: "React Native",
        },
      ],
      technologies: "React Native, Kotlin, Azure DevOps, Azure AD",
    },
    {
      company: "Neoxia Maroc",
      location: "Casablanca, Maroc",
      period: "07/2022 - 09/2022",
      role: "Stage PFA (2 Mois)",
      projects: [
        {
          name: "Application mobile pour gérer le flux d'argent des utilisateurs",
          responsibilities: [],
          githubLink: "#", // Replace with actual GitHub link
        },
      ],
      technologies: "React Native, Redux-persist",
    },
  ]

  return (
    <section
      id="experience"
      className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-900 transition-colors duration-300 overflow-hidden relative"
    >
      <div className="container mx-auto px-6 relative z-10">
        <AnimatedSectionHeader title="Expérience Professionnelle" />
        <div className="space-y-16">
          {experiences.map((exp, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
              className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg transition-all duration-300 hover:shadow-2xl relative overflow-hidden group"
            >
              <div
                className="absolute top-0 right-0 w-32 h-32 bg-blue-200 dark:bg-blue-700 rounded-bl-full z-0 opacity-50 
                transition-transform duration-300 group-hover:scale-110"
              ></div>
              <div className="relative z-10">
                <h3 className="text-2xl font-semibold mb-2 dark:text-white flex items-center">
                  {exp.company === "Xelops Technology (Ex Neoxia Maroc)" ? (
                    <Code className="w-6 h-6 mr-2 text-blue-500" />
                  ) : exp.role === "Stage PFE (6 Mois)" ? (
                    <Server className="w-6 h-6 mr-2 text-green-500" />
                  ) : (
                    <Smartphone className="w-6 h-6 mr-2 text-purple-500" />
                  )}
                  {exp.company}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4 flex items-center">
                  <MapPin className="w-4 h-4 mr-2" />
                  {exp.location}
                </p>
                <p className="text-gray-600 dark:text-gray-300 mb-4 flex items-center">
                  <Calendar className="w-4 h-4 mr-2" />
                  {exp.period}
                </p>
                <p className="text-xl font-medium mb-4 dark:text-gray-200 flex items-center">
                  <Briefcase className="w-5 h-5 mr-2" />
                  {exp.role}
                </p>

                {exp.projects &&
                  exp.projects.map((project, projectIndex) => (
                    <div key={projectIndex} className="mb-6">
                      <h4 className="text-lg font-medium mb-2 text-blue-600 dark:text-blue-400">{project.name}</h4>

                      {project.responsibilities && project.responsibilities.length > 0 && (
                        <ul className="list-none space-y-2 mb-4">
                          {project.responsibilities.map((resp, respIndex) => (
                            <li key={respIndex} className="text-gray-700 dark:text-gray-300 flex items-start">
                              <span className="text-blue-500 mr-2">•</span>
                              {resp}
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  ))}

                {exp.technologies && (
                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <p className="font-medium text-gray-700 dark:text-gray-300">
                      <span className="text-blue-600 dark:text-blue-400">Technologies:</span> {exp.technologies}
                    </p>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
      <div className="absolute bottom-0 right-0 w-64 h-64 -mb-32 -mr-32 opacity-20">
        <Image src="/placeholder.svg?height=256&width=256" alt="Decorative background" width={256} height={256} />
      </div>
    </section>
  )
}

