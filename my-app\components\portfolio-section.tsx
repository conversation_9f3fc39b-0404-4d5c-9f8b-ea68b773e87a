"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { ExternalLink, Code, ArrowRight, Info } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import ProjectDetailsModal from "@/components/project-details-modal"

// Static projects data organized by year
const projectsByYear = {
  2025: [
    {
      id: 1,
      title: "AI-Powered SEO Article Generator (SaaS)",
      description: "SaaS platform for generating SEO-optimized articles using AI to support content creators and digital marketers. Automated content creation with customizable prompts and keyword optimization — currently being developed for production deployment to support efficient content scaling for marketing teams.",
      tags: ["React", "MUI", "Redux Toolkit Query", "Supabase", "Pocketsflow", "VPS", "Vercel", "Hostinger"],
      demo_url: "https://seo-generator-demo.vercel.app",
      code_url: "https://github.com/apachi1444/ai-seo-generator",
      featured: true,
      icon: "✍️",
      status: "In Development",
      year: 2025
    },
    {
      id: 9,
      title: "React Native Scalable Drawer Library",
      description: "Open source React Native library providing customizable and performant drawer components for mobile applications. Designed to offer developers flexible drawer solutions with smooth animations, gesture support, and extensive customization options for enhanced user experience.",
      tags: ["React Native", "TypeScript", "Expo", "Open Source", "NPM Package", "Mobile UI"],
      demo_url: "https://react-native-scalable-drawer-demo.expo.dev",
      code_url: "https://github.com/apachi1444/react-native-scalable-drawer",
      featured: true,
      icon: "📚",
      status: "In Development",
      year: 2025
    },
    {
      id: 10,
      title: "TrackMe - Habit Tracking Mobile App",
      description: "Mobile application for habit tracking and personal productivity enhancement. Features intuitive habit creation, progress tracking, analytics, and motivational insights to help users build and maintain positive habits. Built with modern mobile development practices for optimal user experience.",
      tags: ["Expo", "React Native", "TypeScript", "Mobile App", "Productivity", "Analytics"],
      demo_url: "https://trackme-habits-demo.expo.dev",
      code_url: "https://github.com/apachi1444/trackme-habits",
      featured: true,
      icon: "📈",
      status: "In Development",
      year: 2025
    }
  ],
  2024: [
    {
      id: 11,
      title: "Sales Facilitator App for Telecom Client",
      description: "Ongoing development of internal Android application to assist field agents with telecom product sales and customer registration. Continuously enhancing offline mode with remote server synchronization, Firebase push notifications, and E2E testing with Maestro. Regular improvements through OCR solutions optimization and multi-module architecture refinements.",
      tags: ["Jetpack Compose", "Android Native", "OCR SDK", "Firebase", "Offline Sync", "Maestro E2E", "Jenkins CI/CD"],
      demo_url: "https://telecom-sales-demo.vercel.app",
      code_url: "https://github.com/apachi1444/telecom-sales-app",
      featured: true,
      icon: "📱",
      status: "Ongoing",
      year: 2024
    },
    {
      id: 12,
      title: "Real Estate Syndic Management App (Mobile)",
      description: "Mobile solution for managing payments and communications related to building syndicates. Developed syndic operations with features for payment tracking, document uploads, and notifications — bringing transparency and ease to property managers and residents.",
      tags: ["React Native", "Firebase", "Redux", "Expo", "Push Notifications"],
      demo_url: "https://syndic-app-demo.netlify.app",
      code_url: "https://github.com/apachi1444/syndic-management-app",
      featured: true,
      icon: "🏢",
      status: "Completed",
      year: 2024
    },
    {
      id: 13,
      title: "MovieMatch - QR Code Integration (Contribution)",
      description: "Contributed QR code technology integration to MovieMatch, a Tinder-like app for choosing films between two people. Implemented QR code functionality allowing guests to easily join host sessions by scanning codes, enhancing user experience and session accessibility for collaborative movie selection.",
      tags: ["Android", "Jetpack Compose", "QR Code", "Kotlin", "Collaboration", "Mobile UI"],
      demo_url: "https://moviematch-demo.vercel.app",
      code_url: "https://github.com/friend-username/moviematch-app",
      featured: true,
      icon: "🎬",
      status: "Contribution",
      year: 2024
    }
  ],
  2023: [
    {
      id: 4,
      title: "Web-to-Mobile Migration Project",
      description: "Migrated an existing web app to mobile to broaden user reach and improve accessibility. Seamless mobile adaptation of a previously web-only platform, enhancing user engagement on smartphones and tablets.",
      tags: ["React Native", "Redux", "API Integration"],
      demo_url: "https://mobile-migration-demo.netlify.app",
      code_url: "https://github.com/apachi1444/web-to-mobile-migration",
      featured: false,
      icon: "🔄",
      year: 2023
    }
  ],
  2022: [
    {
      id: 5,
      title: "Student Housing Web App (Marrakech)",
      description: "Web platform for students to easily find and rent apartments in Marrakech. Provided a streamlined rental system with filtering and user-friendly listings, simplifying the housing search for students.",
      tags: ["MongoDB", "Express", "React", "Node.js"],
      demo_url: "https://student-housing-marrakech.vercel.app",
      code_url: "https://github.com/apachi1444/student-housing-marrakech",
      featured: true,
      icon: "🏠",
      year: 2022
    },
    {
      id: 6,
      title: "Budget Tracker Mobile App",
      description: "Offline-first mobile application to help users manage daily expenses. Empowered users to track budgets without internet dependency, enhancing accessibility and usability for personal finance.",
      tags: ["React Native", "SQLite", "Offline Storage"],
      demo_url: "https://budget-tracker-demo.netlify.app",
      code_url: "https://github.com/apachi1444/budget-tracker-mobile",
      featured: true,
      icon: "💸",
      year: 2022
    },
    {
      id: 7,
      title: "Event Organizer App",
      description: "Mobile app connecting event organizers with service providers (singers, chefs, decorators, etc.). Facilitated professional connections and simplified event planning for users, from private parties to large gatherings.",
      tags: ["Flutter", "Firebase", "Spring Boot", "Dart"],
      demo_url: "https://event-organizer-demo.vercel.app",
      code_url: "https://github.com/apachi1444/event-organizer-app",
      featured: true,
      icon: "🎉",
      year: 2022
    }
  ]
}

// Define project type for better TypeScript support
interface Project {
  id: number
  title: string
  description: string
  tags: string[]
  demo_url: string
  code_url: string
  featured: boolean
  icon: string
  year: number
  status?: string
}

// Function to escape apostrophes in a string
function escapeApostrophes(str: string): string {
  return str.replace(/'/g, "&apos;")
}

export default function PortfolioSection() {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const openProjectModal = (project: Project) => {
    setSelectedProject(project)
    setIsModalOpen(true)
  }

  const closeProjectModal = () => {
    setIsModalOpen(false)
    setSelectedProject(null)
  }

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  }

  return (
    <div id="portfolio" className="min-h-screen p-8 md:p-12 bg-gradient-to-br from-amber-50 to-orange-100 dark:from-gray-900 dark:to-amber-950">
      <div className="max-w-6xl mx-auto">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-4xl md:text-5xl font-bold mb-6 text-center lg:text-left"
        >
          My <span className="text-amber-600 dark:text-amber-400">Portfolio</span>
        </motion.h2>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-lg text-muted-foreground mb-12 text-center lg:text-left"
        >
          A chronological journey through my projects and achievements
        </motion.p>

        {/* Projects organized by year */}
        <div className="space-y-16">
          {Object.entries(projectsByYear)
            .sort(([a], [b]) => Number(b) - Number(a)) // Sort years in descending order
            .map(([year, yearProjects]) => (
              <motion.div
                key={year}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="space-y-8"
              >
                {/* Year Header */}
                <div className="flex items-center gap-4">
                  <div className="text-3xl md:text-4xl font-bold text-amber-600 dark:text-amber-400">
                    🚀 {year}
                  </div>
                  <div className="flex-1 h-px bg-gradient-to-r from-amber-300 to-transparent dark:from-amber-600"></div>
                </div>

                {/* Projects Grid for this year */}
                <motion.div
                  variants={container}
                  initial="hidden"
                  animate="show"
                  className="grid grid-cols-1 md:grid-cols-2 gap-8"
                >
                  {yearProjects.map((project) => (
                    <motion.div
                      key={project.id}
                      variants={item}
                      className="group relative overflow-hidden rounded-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <div className="relative aspect-video overflow-hidden bg-gradient-to-br from-amber-100 to-orange-200 dark:from-amber-900 dark:to-orange-900">
                        {/* Project Icon */}
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-6xl opacity-20">{project.icon}</div>
                        </div>

                        {/* Status Badge */}
                        {'status' in project && project.status && (
                          <div className="absolute top-4 right-4">
                            <Badge variant="secondary" className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                              {project.status}
                            </Badge>
                          </div>
                        )}

                        {/* Hover Overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="absolute bottom-0 left-0 right-0 p-6">
                            <div className="flex space-x-2">
                              <Button asChild size="sm" variant="default" className="rounded-full flex-1">
                                <a href={project.demo_url} target="_blank" rel="noopener noreferrer">
                                  <ExternalLink className="mr-2 h-4 w-4" />
                                  Demo
                                </a>
                              </Button>
                              <Button
                                asChild
                                size="sm"
                                variant="outline"
                                className="rounded-full bg-white/20 border-white/40 text-white hover:bg-white/30 flex-1"
                              >
                                <a href={project.code_url} target="_blank" rel="noopener noreferrer">
                                  <Code className="mr-2 h-4 w-4" />
                                  Code
                                </a>
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="p-6">
                        <div className="flex items-start gap-3 mb-2">
                          <span className="text-2xl">{project.icon}</span>
                          <h3 className="text-xl font-semibold flex-1">{project.title}</h3>
                        </div>
                        <p className="text-muted-foreground mb-4 text-sm leading-relaxed line-clamp-3">{escapeApostrophes(project.description)}</p>
                        <div className="flex flex-wrap gap-2 mb-4">
                          {project.tags.slice(0, 3).map((tag) => (
                            <Badge
                              key={tag}
                              variant="secondary"
                              className="bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200 text-xs"
                            >
                              {tag}
                            </Badge>
                          ))}
                          {project.tags.length > 3 && (
                            <Badge variant="secondary" className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 text-xs">
                              +{project.tags.length - 3} more
                            </Badge>
                          )}
                        </div>
                        <Button
                          onClick={() => openProjectModal(project)}
                          variant="outline"
                          size="sm"
                          className="w-full rounded-full group"
                        >
                          <Info className="mr-2 h-4 w-4" />
                          View Details
                          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                        </Button>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </motion.div>
            ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-12 text-center"
        >
          <Button variant="outline" size="lg" className="rounded-full group">
            View All Projects
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </motion.div>
      </div>

      {/* Project Details Modal */}
      {selectedProject && (
        <ProjectDetailsModal
          project={selectedProject}
          isOpen={isModalOpen}
          onClose={closeProjectModal}
        />
      )}
    </div>
  )
}

