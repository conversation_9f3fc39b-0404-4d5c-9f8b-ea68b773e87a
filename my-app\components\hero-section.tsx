"use client"

import { ArrowR<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import Link from "next/link"
import Image from "next/image"

export default function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center pt-20">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-0 right-0 -mt-16 -mr-16 h-64 w-64 rounded-full bg-orange-500/20 blur-3xl" />
        <div className="absolute bottom-0 left-0 -mb-16 -ml-16 h-64 w-64 rounded-full bg-blue-500/20 blur-3xl" />
      </div>

      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center lg:text-left"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              Hi, I&apos;m <span className="text-orange-500">John Doe</span>
            </h1>
            <h2 className="text-2xl md:text-3xl font-medium mb-6 text-muted-foreground">Mobile & Web Developer</h2>
            <p className="text-lg mb-8 max-w-lg mx-auto lg:mx-0 text-muted-foreground">
              I create beautiful, functional, and user-friendly applications that solve real-world problems and deliver
              exceptional user experiences.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button
                size="lg"
                className="rounded-full group"
                onClick={() => {
                  // Update URL and trigger section change
                  window.history.pushState({}, "", "#portfolio")

                  // For mobile view, scroll to portfolio section
                  if (window.innerWidth < 1024) {
                    const portfolioSection = document.getElementById("portfolio")
                    if (portfolioSection) {
                      portfolioSection.scrollIntoView({ behavior: "smooth" })
                    }
                  } else {
                    // For desktop view, trigger a custom event to change section
                    window.dispatchEvent(new CustomEvent('navigate-to-section', {
                      detail: { section: 'portfolio' }
                    }))
                  }
                }}
              >
                View My Work
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
              <Button asChild variant="outline" size="lg" className="rounded-full">
                <Link href="/contact">Contact Me</Link>
              </Button>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="relative"
          >
            <div className="relative mx-auto lg:ml-auto lg:mr-0 w-72 h-72 md:w-96 md:h-96">
              {/* Decorative elements */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-orange-500 to-blue-500 blur-md opacity-20 animate-pulse" />
              <div className="absolute -inset-0.5 rounded-full bg-gradient-to-r from-orange-500 to-blue-500 opacity-50" />

              {/* Profile image */}
              <div className="absolute inset-2 rounded-full overflow-hidden bg-background">
                <Image
                  src="/placeholder.svg?height=400&width=400"
                  alt="John Doe"
                  width={400}
                  height={400}
                  className="object-cover w-full h-full"
                  priority
                />
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

