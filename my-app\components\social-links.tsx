"use client"

import type React from "react"
import { Gith<PERSON>, Linkedin, Twitter, Instagram, Facebook, Youtube, Dribbble, Figma } from "lucide-react"
import { cn } from "@/lib/utils"

interface SocialLinksProps {
  className?: string
  iconClassName?: string
}

// Static social links data
const socialLinks = [
  {
    id: 1,
    name: "GitHub",
    url: "https://github.com/apachi1444",
    icon: "github",
  },
  {
    id: 2,
    name: "LinkedIn",
    url: "https://linkedin.com/in/yessine-jaoua",
    icon: "linkedin",
  },
  {
    id: 3,
    name: "Twitter",
    url: "https://twitter.com/JawaYessine",
    icon: "twitter",
  },
]

// Map of icon names to components
const iconMap: Record<string, React.ReactNode> = {
  github: <Github />,
  linkedin: <Linkedin />,
  twitter: <Twitter />,
  instagram: <Instagram />,
  facebook: <Facebook />,
  youtube: <Youtube />,
  dribbble: <Dribbble />,
  figma: <Figma />,
}

export default function SocialLinks({ className, iconClassName }: SocialLinksProps) {
  return (
    <div className={cn("flex space-x-4", className)}>
      {socialLinks.map((link) => (
        <a
          key={link.id}
          href={link.url}
          target="_blank"
          rel="noopener noreferrer"
          className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors duration-300"
          aria-label={link.name}
        >
          <div className={cn("h-5 w-5", iconClassName)}>{iconMap[link.icon.toLowerCase()] || <Github />}</div>
        </a>
      ))}
    </div>
  )
}

